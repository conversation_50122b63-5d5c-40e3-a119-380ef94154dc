#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Duration控制技术深入研究
探索FFmpeg中实现精确帧时长控制的多种方法
"""

import os
import subprocess
import tempfile
import shutil
import time

class DurationControlResearch:
    """Duration控制技术研究"""
    
    def __init__(self):
        self.ffmpeg_cmd = 'ffmpeg'
        
    def method_1_filter_complex_setpts(self, frames_a_dir, frames_b_dir, output_path):
        """方法1: 使用filter_complex和setpts滤镜控制时间戳"""
        print("🔬 方法1: filter_complex + setpts时间戳控制")
        print("-" * 60)
        
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        print(f"A帧数: {count_a}, B帧数: {count_b}")
        
        # 创建临时视频文件
        temp_dir = tempfile.mkdtemp(prefix='method1_')
        
        try:
            # 创建A帧视频
            video_a_temp = os.path.join(temp_dir, 'video_a.mp4')
            cmd_a = [
                self.ffmpeg_cmd, '-y',
                '-framerate', '30',
                '-i', f'{frames_a_dir}/frame_%06d.png',
                '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                video_a_temp
            ]
            
            result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
            if result_a.returncode != 0:
                print(f"❌ A视频创建失败: {result_a.stderr}")
                return False
            
            # 创建B帧视频
            video_b_temp = os.path.join(temp_dir, 'video_b.mp4')
            cmd_b = [
                self.ffmpeg_cmd, '-y',
                '-framerate', '30',
                '-i', f'{frames_b_dir}/frame_%06d.png',
                '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                video_b_temp
            ]
            
            result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
            if result_b.returncode != 0:
                print(f"❌ B视频创建失败: {result_b.stderr}")
                return False
            
            # 使用filter_complex进行时间戳操作
            filter_complex = (
                "[0:v]setpts='if(eq(mod(n,2),0),PTS,PTS-0.032333*TB)'[a_adjusted];"
                "[1:v]setpts='PTS+0.033333*TB*floor(n/1)'[b_adjusted];"
                "[a_adjusted][b_adjusted]interleave=nb_inputs=2:duration=shortest[v]"
            )
            
            cmd_merge = [
                self.ffmpeg_cmd, '-y',
                '-i', video_a_temp,
                '-i', video_b_temp,
                '-filter_complex', filter_complex,
                '-map', '[v]',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-r', '30',
                output_path
            ]
            
            print("执行filter_complex时间戳控制...")
            result = subprocess.run(cmd_merge, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 方法1执行成功")
                return True
            else:
                print(f"❌ 方法1失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def method_2_minterpolate_fps(self, frames_a_dir, frames_b_dir, output_path):
        """方法2: 使用minterpolate滤镜进行帧率控制"""
        print("🔬 方法2: minterpolate帧率插值控制")
        print("-" * 60)
        
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        # 创建交替序列
        temp_dir = tempfile.mkdtemp(prefix='method2_')
        interleaved_dir = os.path.join(temp_dir, 'interleaved')
        os.makedirs(interleaved_dir, exist_ok=True)
        
        try:
            frame_index = 0
            for i in range(min(count_a, count_b)):
                # A帧
                src_a = os.path.join(frames_a_dir, frames_a[i])
                dst_a = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_a, dst_a)
                frame_index += 1
                
                # B帧
                src_b = os.path.join(frames_b_dir, frames_b[i])
                dst_b = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_b, dst_b)
                frame_index += 1
            
            # 使用minterpolate进行帧率控制
            cmd = [
                self.ffmpeg_cmd, '-y',
                '-framerate', '60',  # 输入60fps
                '-i', f'{interleaved_dir}/frame_%06d.png',
                '-vf', 'minterpolate=fps=30:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-pix_fmt', 'yuv420p',
                output_path
            ]
            
            print("执行minterpolate帧率控制...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 方法2执行成功")
                return True
            else:
                print(f"❌ 方法2失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def method_3_variable_framerate(self, frames_a_dir, frames_b_dir, output_path):
        """方法3: 使用可变帧率(VFR)和精确时间戳"""
        print("🔬 方法3: 可变帧率(VFR)精确时间戳控制")
        print("-" * 60)
        
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        # 创建精确时间戳文件
        temp_dir = tempfile.mkdtemp(prefix='method3_')
        
        try:
            # 创建交替序列
            interleaved_dir = os.path.join(temp_dir, 'interleaved')
            os.makedirs(interleaved_dir, exist_ok=True)
            
            # 创建时间戳文件
            timestamps_file = os.path.join(temp_dir, 'timestamps.txt')
            
            frame_index = 0
            current_time = 0.0
            
            with open(timestamps_file, 'w') as f:
                for i in range(min(count_a, count_b)):
                    # A帧 - 标准时长
                    src_a = os.path.join(frames_a_dir, frames_a[i])
                    dst_a = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                    shutil.copy2(src_a, dst_a)
                    
                    f.write(f"{current_time:.9f}\n")
                    current_time += 0.033333  # 30fps标准间隔
                    frame_index += 1
                    
                    # B帧 - 微秒时长
                    src_b = os.path.join(frames_b_dir, frames_b[i])
                    dst_b = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                    shutil.copy2(src_b, dst_b)
                    
                    f.write(f"{current_time:.9f}\n")
                    current_time += 0.000001  # 1微秒间隔
                    frame_index += 1
            
            # 使用-vf setpts从文件读取时间戳
            cmd = [
                self.ffmpeg_cmd, '-y',
                '-f', 'image2',
                '-framerate', '1000',  # 高帧率输入
                '-i', f'{interleaved_dir}/frame_%06d.png',
                '-vf', f'setpts=\'if(eq(n,0),0,if(eq(mod(n,2),0),PTS+0.033333*TB,PTS+0.000001*TB))\'',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-pix_fmt', 'yuv420p',
                '-vsync', 'vfr',  # 可变帧率
                output_path
            ]
            
            print("执行VFR时间戳控制...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 方法3执行成功")
                return True
            else:
                print(f"❌ 方法3失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def method_4_custom_pts_calculation(self, frames_a_dir, frames_b_dir, output_path):
        """方法4: 自定义PTS计算和帧时长控制"""
        print("🔬 方法4: 自定义PTS计算精确控制")
        print("-" * 60)
        
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        temp_dir = tempfile.mkdtemp(prefix='method4_')
        
        try:
            # 创建交替序列
            interleaved_dir = os.path.join(temp_dir, 'interleaved')
            os.makedirs(interleaved_dir, exist_ok=True)
            
            frame_index = 0
            for i in range(min(count_a, count_b)):
                # A帧
                src_a = os.path.join(frames_a_dir, frames_a[i])
                dst_a = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_a, dst_a)
                frame_index += 1
                
                # B帧
                src_b = os.path.join(frames_b_dir, frames_b[i])
                dst_b = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_b, dst_b)
                frame_index += 1
            
            # 使用复杂的setpts表达式
            pts_expression = (
                "if(eq(mod(n,2),0),"  # 如果是偶数帧(A帧)
                "n*0.033333*TB,"      # 使用标准30fps时间戳
                "((n-1)*0.033333+0.000001)*TB"  # 如果是奇数帧(B帧)，使用微秒偏移
                ")"
            )
            
            cmd = [
                self.ffmpeg_cmd, '-y',
                '-framerate', '60',
                '-i', f'{interleaved_dir}/frame_%06d.png',
                '-vf', f'setpts=\'{pts_expression}\'',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-pix_fmt', 'yuv420p',
                '-vsync', 'vfr',
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]
            
            print("执行自定义PTS计算...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 方法4执行成功")
                return True
            else:
                print(f"❌ 方法4失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def method_5_fps_filter_control(self, frames_a_dir, frames_b_dir, output_path):
        """方法5: 使用fps滤镜进行精确帧率控制"""
        print("🔬 方法5: fps滤镜精确帧率控制")
        print("-" * 60)
        
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        temp_dir = tempfile.mkdtemp(prefix='method5_')
        
        try:
            # 创建交替序列
            interleaved_dir = os.path.join(temp_dir, 'interleaved')
            os.makedirs(interleaved_dir, exist_ok=True)
            
            frame_index = 0
            for i in range(min(count_a, count_b)):
                # A帧
                src_a = os.path.join(frames_a_dir, frames_a[i])
                dst_a = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_a, dst_a)
                frame_index += 1
                
                # B帧
                src_b = os.path.join(frames_b_dir, frames_b[i])
                dst_b = os.path.join(interleaved_dir, f'frame_{frame_index:06d}.png')
                shutil.copy2(src_b, dst_b)
                frame_index += 1
            
            # 使用fps滤镜和复杂的时间控制
            filter_complex = (
                "[0:v]fps=fps=60000/1001:round=down,"  # 精确帧率控制
                "setpts='if(eq(mod(n,2),0),PTS,PTS-0.032332*TB)'"  # 时间戳调整
                "[v]"
            )
            
            cmd = [
                self.ffmpeg_cmd, '-y',
                '-framerate', '60',
                '-i', f'{interleaved_dir}/frame_%06d.png',
                '-filter_complex', filter_complex,
                '-map', '[v]',
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-pix_fmt', 'yuv420p',
                '-r', '30',
                output_path
            ]
            
            print("执行fps滤镜控制...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ 方法5执行成功")
                return True
            else:
                print(f"❌ 方法5失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
    
    def analyze_duration_effectiveness(self, video_path, method_name):
        """分析duration控制效果"""
        print(f"\n📊 {method_name} - Duration控制效果分析")
        print("-" * 60)
        
        # 获取前20帧的时间戳
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-select_streams', 'v:0',
            '-show_entries', 'frame=n,best_effort_timestamp_time',
            '-of', 'csv=p=0',
            '-read_intervals', '%+#20',
            video_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                timestamps = []
                
                for line in lines:
                    if line and ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            try:
                                frame_num = int(parts[0])
                                timestamp = float(parts[1])
                                timestamps.append((frame_num, timestamp))
                            except:
                                continue
                
                if len(timestamps) >= 4:
                    print("帧时间戳分析:")
                    intervals = []
                    
                    for i in range(min(10, len(timestamps))):
                        frame_num, timestamp = timestamps[i]
                        if i > 0:
                            interval = timestamp - timestamps[i-1][1]
                            intervals.append(interval)
                            frame_type = "A帧" if frame_num % 2 == 0 else "B帧"
                            print(f"  帧{frame_num}: {timestamp:.9f}s (间隔: {interval:.9f}s) - {frame_type}")
                        else:
                            print(f"  帧{frame_num}: {timestamp:.9f}s (起始帧)")
                    
                    # 检查是否有微秒级间隔
                    micro_intervals = [i for i in intervals if i < 0.00001]
                    if micro_intervals:
                        print(f"✅ 检测到{len(micro_intervals)}个微秒级间隔")
                        print(f"   最短间隔: {min(micro_intervals):.9f}秒")
                        return True
                    else:
                        print("❌ 未检测到微秒级间隔")
                        return False
                else:
                    print("❌ 时间戳数据不足")
                    return False
            else:
                print(f"❌ FFprobe执行失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return False
    
    def comprehensive_duration_research(self, video_a_path, video_b_path):
        """综合duration控制研究"""
        print("🔬 FFmpeg Duration控制技术综合研究")
        print("=" * 80)
        print("目标: 实现B帧微秒级时长控制，保持1:1 AB融帧结构")
        print()
        
        # 提取帧
        temp_dir = tempfile.mkdtemp(prefix='duration_research_')
        
        try:
            # 提取A帧
            frames_a_dir = os.path.join(temp_dir, 'frames_a')
            os.makedirs(frames_a_dir, exist_ok=True)
            
            cmd_a = [
                self.ffmpeg_cmd, '-y', '-i', video_a_path,
                '-vframes', '10',  # 只提取前10帧用于测试
                f'{frames_a_dir}/frame_%06d.png'
            ]
            
            result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=60)
            if result_a.returncode != 0:
                print(f"❌ A帧提取失败")
                return
            
            # 提取B帧
            frames_b_dir = os.path.join(temp_dir, 'frames_b')
            os.makedirs(frames_b_dir, exist_ok=True)
            
            cmd_b = [
                self.ffmpeg_cmd, '-y', '-i', video_b_path,
                '-vframes', '10',  # 只提取前10帧用于测试
                f'{frames_b_dir}/frame_%06d.png'
            ]
            
            result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=60)
            if result_b.returncode != 0:
                print(f"❌ B帧提取失败")
                return
            
            print("✓ 测试帧提取完成")
            
            # 测试各种方法
            methods = [
                ("方法1: filter_complex+setpts", self.method_1_filter_complex_setpts, "method1_test.mp4"),
                ("方法2: minterpolate帧率控制", self.method_2_minterpolate_fps, "method2_test.mp4"),
                ("方法3: 可变帧率VFR", self.method_3_variable_framerate, "method3_test.mp4"),
                ("方法4: 自定义PTS计算", self.method_4_custom_pts_calculation, "method4_test.mp4"),
                ("方法5: fps滤镜控制", self.method_5_fps_filter_control, "method5_test.mp4"),
            ]
            
            successful_methods = []
            
            for method_name, method_func, output_file in methods:
                print(f"\n{'='*20} {method_name} {'='*20}")
                
                success = method_func(frames_a_dir, frames_b_dir, output_file)
                
                if success and os.path.exists(output_file):
                    # 分析效果
                    duration_effective = self.analyze_duration_effectiveness(output_file, method_name)
                    
                    if duration_effective:
                        successful_methods.append((method_name, output_file))
                        print(f"✅ {method_name} - Duration控制有效!")
                    else:
                        print(f"⚠️ {method_name} - Duration控制无效")
                else:
                    print(f"❌ {method_name} - 执行失败")
            
            # 总结研究结果
            print(f"\n📊 Duration控制研究总结")
            print("=" * 80)
            
            if successful_methods:
                print(f"✅ 发现{len(successful_methods)}个有效方法:")
                for method_name, output_file in successful_methods:
                    print(f"  • {method_name}: {output_file}")
                
                print(f"\n🎯 推荐使用最有效的方法进行完整实现")
                return successful_methods[0]  # 返回第一个成功的方法
            else:
                print("❌ 所有方法都无法实现有效的duration控制")
                print("🔍 需要探索其他技术方案:")
                print("  • 使用专业视频编辑库 (如OpenCV)")
                print("  • 直接操作视频容器格式")
                print("  • 使用其他编码工具")
                return None
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)


def main():
    """主程序"""
    print("🔬 Duration控制技术深入研究")
    print("=" * 80)
    
    # 输入文件
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    researcher = DurationControlResearch()
    
    # 执行综合研究
    successful_method = researcher.comprehensive_duration_research(video_a_path, video_b_path)
    
    if successful_method:
        method_name, test_file = successful_method
        print(f"\n🎉 Duration控制研究成功!")
        print(f"有效方法: {method_name}")
        print(f"测试文件: {test_file}")
        print(f"下一步: 使用此方法实现完整的AB融帧视频")
    else:
        print(f"\n⚠️ Duration控制研究未找到有效方案")
        print(f"建议: 探索FFmpeg之外的技术方案")


if __name__ == "__main__":
    main()
