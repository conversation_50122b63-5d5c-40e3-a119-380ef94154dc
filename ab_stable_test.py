#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术稳定版本测试脚本
修复帧率和音频同步问题
"""

import os
import time
import subprocess
from ab_frame_interleaving_toolkit import ABFrameInterleavingToolkit

def get_detailed_video_info(video_path):
    """获取详细的视频信息"""
    try:
        cmd = ['ffprobe', '-v', 'error', '-show_format', '-show_streams', '-of', 'json', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)
            
            video_stream = None
            audio_stream = None
            
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                elif stream.get('codec_type') == 'audio':
                    audio_stream = stream
            
            format_info = data.get('format', {})
            
            return {
                'video': video_stream,
                'audio': audio_stream,
                'format': format_info
            }
    except Exception as e:
        print(f"获取视频信息失败: {e}")
    
    return None

def analyze_frame_consistency(video_path):
    """分析帧一致性"""
    try:
        # 获取帧信息
        cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', 
               '-show_entries', 'frame=n,pkt_pts_time,pict_type', 
               '-of', 'csv=p=0', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            frames = []
            
            for line in lines:
                if line and ',' in line:
                    parts = line.split(',')
                    if len(parts) >= 3:
                        try:
                            frame_num = int(parts[0])
                            pts_time = float(parts[1])
                            frame_type = parts[2]
                            frames.append({
                                'num': frame_num,
                                'pts': pts_time,
                                'type': frame_type
                            })
                        except:
                            continue
            
            if frames:
                # 计算帧间隔
                intervals = []
                for i in range(1, len(frames)):
                    interval = frames[i]['pts'] - frames[i-1]['pts']
                    intervals.append(interval)
                
                avg_interval = sum(intervals) / len(intervals) if intervals else 0
                
                return {
                    'total_frames': len(frames),
                    'avg_interval': avg_interval,
                    'calculated_fps': 1.0 / avg_interval if avg_interval > 0 else 0,
                    'frame_types': [f['type'] for f in frames[:10]]  # 前10帧的类型
                }
    except Exception as e:
        print(f"帧一致性分析失败: {e}")
    
    return None

def main():
    print("🔧 AB融帧技术稳定版本测试")
    print("=" * 80)
    
    # 输入文件路径
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    # 输出文件路径
    stable_output_path = "ab_interleaved_stable.mp4"
    extracted_a_stable = "extracted_a_stable.mp4"
    extracted_b_stable = "extracted_b_stable.mp4"
    
    # 初始化工具包
    print("🔧 初始化稳定版AB融帧工具包...")
    toolkit = ABFrameInterleavingToolkit()
    
    try:
        # 步骤1: 分析输入视频
        print("\n📋 步骤1: 分析输入视频详细信息")
        print("-" * 50)
        
        info_a = get_detailed_video_info(video_a_path)
        info_b = get_detailed_video_info(video_b_path)
        
        if info_a and info_a['video']:
            v = info_a['video']
            a = info_a['audio']
            print(f"视频A: {v['width']}x{v['height']}, {v['nb_frames']}帧, {float(info_a['format']['duration']):.2f}s")
            if a:
                print(f"       音频: {a['codec_name']}, {a.get('sample_rate', 'N/A')}Hz")
        
        if info_b and info_b['video']:
            v = info_b['video']
            a = info_b['audio']
            print(f"视频B: {v['width']}x{v['height']}, {v['nb_frames']}帧, {float(info_b['format']['duration']):.2f}s")
            if a:
                print(f"       音频: {a['codec_name']}, {a.get('sample_rate', 'N/A')}Hz")
        
        # 步骤2: 创建稳定版AB融帧视频
        print("\n🎬 步骤2: 创建稳定版AB融帧视频")
        print("-" * 50)
        
        start_time = time.time()
        success = toolkit.create_ab_interleaved_video(
            video_a_path=video_a_path,
            video_b_path=video_b_path,
            output_path=stable_output_path,
            resolution='vertical_hd'
        )
        creation_time = time.time() - start_time
        
        if success and os.path.exists(stable_output_path):
            file_size = os.path.getsize(stable_output_path) / 1024 / 1024
            print(f"✅ 稳定版AB融帧视频创建成功!")
            print(f"📁 输出文件: {stable_output_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            print(f"⏱️ 创建耗时: {creation_time:.2f} 秒")
            
        else:
            print("❌ 稳定版AB融帧视频创建失败")
            return False
        
        # 步骤3: 详细分析输出视频
        print("\n🔍 步骤3: 详细分析输出视频")
        print("-" * 50)
        
        output_info = get_detailed_video_info(stable_output_path)
        if output_info:
            v = output_info['video']
            a = output_info['audio']
            f = output_info['format']
            
            print(f"📺 视频信息:")
            print(f"   分辨率: {v['width']}x{v['height']}")
            print(f"   帧数: {v['nb_frames']}")
            print(f"   时长: {float(f['duration']):.3f}秒")
            print(f"   帧率: {v['r_frame_rate']} ({eval(v['r_frame_rate']):.2f}fps)")
            print(f"   编码: {v['codec_name']}")
            
            if a:
                print(f"🔊 音频信息:")
                print(f"   编码: {a['codec_name']}")
                print(f"   采样率: {a.get('sample_rate', 'N/A')}Hz")
                print(f"   声道: {a.get('channels', 'N/A')}")
                print(f"   时长: {float(a.get('duration', 0)):.3f}秒")
            
            # 检查音视频同步
            video_duration = float(f['duration'])
            audio_duration = float(a.get('duration', 0)) if a else 0
            sync_diff = abs(video_duration - audio_duration)
            
            print(f"🎯 同步检查:")
            print(f"   视频时长: {video_duration:.3f}秒")
            print(f"   音频时长: {audio_duration:.3f}秒")
            print(f"   时长差异: {sync_diff:.3f}秒")
            
            if sync_diff < 0.1:
                print("   ✅ 音视频同步良好")
            else:
                print("   ⚠️ 音视频可能不同步")
        
        # 步骤4: 帧一致性分析
        print("\n📊 步骤4: 帧一致性分析")
        print("-" * 50)
        
        frame_analysis = analyze_frame_consistency(stable_output_path)
        if frame_analysis:
            print(f"📈 帧分析结果:")
            print(f"   总帧数: {frame_analysis['total_frames']}")
            print(f"   平均帧间隔: {frame_analysis['avg_interval']:.6f}秒")
            print(f"   计算帧率: {frame_analysis['calculated_fps']:.2f}fps")
            print(f"   前10帧类型: {frame_analysis['frame_types']}")
            
            # 检查帧率稳定性
            expected_interval = 1.0 / 30.0  # 30fps
            interval_diff = abs(frame_analysis['avg_interval'] - expected_interval)
            
            if interval_diff < 0.001:
                print("   ✅ 帧率稳定")
            else:
                print(f"   ⚠️ 帧率可能不稳定，偏差: {interval_diff:.6f}秒")
        
        # 步骤5: 提取测试
        print("\n🎯 步骤5: 提取测试")
        print("-" * 50)
        
        # 提取A视频
        success_a = toolkit.extract_hidden_video(stable_output_path, extracted_a_stable, 'A')
        # 提取B视频
        success_b = toolkit.extract_hidden_video(stable_output_path, extracted_b_stable, 'B')
        
        if success_a and success_b:
            print("✅ 视频提取成功!")
            
            # 分析提取的视频
            extracted_a_info = get_detailed_video_info(extracted_a_stable)
            extracted_b_info = get_detailed_video_info(extracted_b_stable)
            
            if extracted_a_info and extracted_a_info['video']:
                v = extracted_a_info['video']
                print(f"📺 提取的A视频: {v['nb_frames']}帧, {float(extracted_a_info['format']['duration']):.2f}s")
            
            if extracted_b_info and extracted_b_info['video']:
                v = extracted_b_info['video']
                print(f"📺 提取的B视频: {v['nb_frames']}帧, {float(extracted_b_info['format']['duration']):.2f}s")
        
        # 步骤6: 对比分析
        print("\n📊 步骤6: 与修复前版本对比")
        print("-" * 50)
        
        # 对比文件
        comparison_files = [
            ("原始版本", "ab_interleaved_test_output.mp4"),
            ("修复版本", "ab_interleaved_fixed.mp4"),
            ("稳定版本", stable_output_path)
        ]
        
        print("版本对比:")
        for name, file_path in comparison_files:
            if os.path.exists(file_path):
                info = get_detailed_video_info(file_path)
                if info and info['video']:
                    v = info['video']
                    f = info['format']
                    a = info['audio']
                    
                    print(f"{name}:")
                    print(f"  帧数: {v['nb_frames']}, 时长: {float(f['duration']):.2f}s")
                    print(f"  音频: {'有' if a else '无'}")
                    print(f"  大小: {os.path.getsize(file_path)/1024/1024:.2f}MB")
        
        print("\n🎉 稳定版测试完成!")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
        return False

if __name__ == "__main__":
    main()
