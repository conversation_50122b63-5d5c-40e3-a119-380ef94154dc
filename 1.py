#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ABT - 双视频处理工具 (修复版)
实现自然时间戳的双视频合并，避免平台检测
"""

import os
import sys
import subprocess
import shutil
import random
import math


# ============================================================================
# 工具函数
# ============================================================================

def check_ffmpeg():
    """检查FFmpeg工具是否可用，返回(ffmpeg_cmd, ffprobe_cmd)"""
    ffmpeg_cmd = None
    ffprobe_cmd = None
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffmpeg_cmd = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                ffprobe_cmd = cmd
                break
        except:
            continue
    
    return ffmpeg_cmd, ffprobe_cmd


def clean_field(field_str):
    """清理字段值，去除逗号、空白和其他无效字符"""
    if not field_str or field_str == 'N/A':
        return ''
    cleaned = field_str.strip().rstrip(',')
    return cleaned


def get_video_info(video_path):
    """获取视频信息"""
    ffmpeg_cmd, ffprobe_cmd = check_ffmpeg()
    if not ffprobe_cmd:
        print("错误: 找不到ffprobe命令")
        return None

    try:
        # 获取基本信息
        cmd = [ffprobe_cmd, '-v', 'error', '-select_streams', 'v:0',
               '-show_entries', 'stream=width,height,r_frame_rate',
               '-show_entries', 'format=duration',
               '-of', 'csv=p=0', video_path]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode != 0:
            print(f"ffprobe命令执行失败: {result.stderr}")
            return None

        lines = result.stdout.strip().split('\n')
        if len(lines) < 2:
            return None

        # 解析信息
        video_fields = [clean_field(f) for f in lines[0].split(',')]
        video_fields = [f for f in video_fields if f]
        format_fields = [clean_field(f) for f in lines[1].split(',')]
        format_fields = [f for f in format_fields if f]

        if len(video_fields) < 3 or len(format_fields) < 1:
            return None

        try:
            width = int(video_fields[0])
            height = int(video_fields[1])
            
            # 解析帧率
            fps_str = video_fields[2]
            if '/' in fps_str:
                num, den = fps_str.split('/')
                fps = float(num) / float(den) if float(den) != 0 else 0
            else:
                fps = 0

            duration = float(format_fields[0])
        except (ValueError, ZeroDivisionError):
            return None
        
        # 获取准确帧数
        cmd_frames = [ffprobe_cmd, '-v', 'error', '-count_frames',
                      '-select_streams', 'v:0', '-show_entries', 'stream=nb_frames',
                      '-of', 'csv=p=0', video_path]

        result_frames = subprocess.run(cmd_frames, capture_output=True, text=True, timeout=60)
        total_frames = 0
        if result_frames.returncode == 0:
            frame_count_str = clean_field(result_frames.stdout.strip())
            if frame_count_str and frame_count_str != 'N/A':
                try:
                    total_frames = int(frame_count_str)
                except ValueError:
                    total_frames = 0
        
        return {
            'width': width,
            'height': height,
            'fps': fps,
            'duration': duration,
            'total_frames': total_frames
        }
        
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None


def extract_frames(video_path, output_dir):
    """提取视频帧"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        print("错误: 找不到ffmpeg命令")
        return False
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [ffmpeg_cmd, '-i', video_path,
               f'{output_dir}/frame_%06d.png']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return True
        else:
            print(f"帧提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"帧提取异常: {e}")
        return False


def generate_stealth_b_timestamps(frame_sequence, base_fps):
    """生成隐蔽的B帧时间戳，既能跳过B帧又避免检测"""
    timestamps = []
    current_time = 0.0
    
    base_interval = 1.0 / base_fps
    random.seed(42)  # 固定随机种子
    
    print(f"生成隐蔽B帧时间戳:")
    print(f"  基础帧率: {base_fps:.2f}fps")
    print(f"  策略: B帧使用微小时间差，但加入自然变化")

    for i, frame_type in enumerate(frame_sequence):
        timestamps.append(current_time)
        
        if frame_type == 'B':
            # B帧：使用很小的时间增量，但不是固定值
            # 在10-50微秒之间随机，看起来像编码器的舍入误差
            b_interval = random.uniform(0.00001, 0.00005)  # 10-50微秒
            current_time += b_interval
        else:
            # I/P帧：使用正常间隔加抖动
            jitter = random.uniform(-0.001, 0.001)  # ±1ms抖动
            
            # 偶尔加入更大的延迟，模拟网络抖动或编码延迟
            if i > 0 and random.random() < 0.05:  # 5%概率
                extra_delay = random.uniform(0.002, 0.010)  # 2-10ms额外延迟
                current_time += base_interval + jitter + extra_delay
            else:
                current_time += base_interval + jitter
        
        # 确保时间戳单调递增
        if i > 0 and timestamps[i] <= timestamps[i-1]:
            timestamps[i] = timestamps[i-1] + random.uniform(0.000001, 0.000010)

    return timestamps


def normalize_framerate(calculated_fps, original_fps):
    """规范化帧率，避免异常值"""
    # 常见的标准帧率
    standard_fps = [23.976, 24, 25, 29.97, 30, 50, 59.94, 60]
    
    # 如果计算出的帧率接近某个标准帧率，则使用标准帧率
    for std_fps in standard_fps:
        if abs(calculated_fps - std_fps) < 0.5:
            print(f"规范化帧率: {calculated_fps:.3f} -> {std_fps}")
            return std_fps
    
    # 如果不接近标准帧率，但在合理范围内，则使用原始视频帧率
    if 10 <= calculated_fps <= 120:
        # 四舍五入到1位小数
        normalized = round(calculated_fps, 1)
        print(f"使用计算帧率: {normalized}")
        return normalized
    else:
        # 异常帧率，使用原始视频帧率
        print(f"异常帧率 {calculated_fps:.3f}，使用原始帧率: {original_fps}")
        return original_fps


# ============================================================================
# 核心算法
# ============================================================================

def arrange_frames_by_sequence(frames_a_dir, frames_b_dir, sequence_dir, frame_sequence, total_frames_a, total_frames_b):
    """根据帧序列将A帧和B帧按正确顺序重新编号排列"""
    try:
        os.makedirs(sequence_dir, exist_ok=True)

        a_frame_index = 1
        b_frame_index = 1

        for i, frame_type in enumerate(frame_sequence):
            target_file = os.path.join(sequence_dir, f'frame_{i+1:06d}.png')

            if frame_type == 'I' or frame_type == 'P':
                # 使用A视频的帧，循环使用
                use_a_frame = ((a_frame_index - 1) % total_frames_a) + 1
                source_file = os.path.join(frames_a_dir, f'frame_{use_a_frame:06d}.png')
                if os.path.exists(source_file):
                    shutil.copy2(source_file, target_file)
                    a_frame_index += 1
                else:
                    print(f"错误: A视频帧{use_a_frame}不存在")
                    return False

            elif frame_type == 'B':
                # 使用B视频的帧，循环使用
                use_b_frame = ((b_frame_index - 1) % total_frames_b) + 1
                source_file = os.path.join(frames_b_dir, f'frame_{use_b_frame:06d}.png')
                if os.path.exists(source_file):
                    shutil.copy2(source_file, target_file)
                    b_frame_index += 1
                else:
                    print(f"错误: B视频帧{use_b_frame}不存在")
                    return False

        return True

    except Exception as e:
        print(f"帧排列失败: {e}")
        return False


def create_adaptive_frame_sequence(total_frames_a, total_frames_b):
    """创建自适应的帧序列，更接近真实编码器行为"""
    
    # 计算合理的目标帧数：使用较短视频的1.5-2倍
    min_frames = min(total_frames_a, total_frames_b)
    max_frames = max(total_frames_a, total_frames_b)
    
    # 目标帧数：在min_frames * 1.5 到 max_frames * 1.2 之间
    target_frames = int(min_frames * 1.8)
    target_frames = min(target_frames, max_frames * 2)  # 不超过最长视频的2倍
    
    print(f"自适应帧序列生成:")
    print(f"  A视频帧数: {total_frames_a}")
    print(f"  B视频帧数: {total_frames_b}")
    print(f"  目标总帧数: {target_frames}")

    sequence = []
    
    # 更自然的GOP结构
    gop_size = 60  # 60帧GOP，2秒@30fps
    
    for i in range(target_frames):
        # GOP开始：I帧
        if i % gop_size == 0:
            sequence.append('I')
        # GOP内的结构：更自然的P/B分布
        elif i % gop_size < 5:
            # GOP前几帧主要是P帧
            sequence.append('P')
        else:
            # 其余位置：70% B帧，30% P帧
            if random.random() < 0.7:
                sequence.append('B')
            else:
                sequence.append('P')
    
    print(f"  生成序列长度: {len(sequence)}")
    i_count = sequence.count('I')
    p_count = sequence.count('P')
    b_count = sequence.count('B')
    print(f"  帧类型分布: I={i_count}, P={p_count}, B={b_count}")
    
    return sequence


# ============================================================================
# 主要处理函数
# ============================================================================

def encode_video_with_pts_manipulation(frame_sequence, temp_dir, output_path, video_a_path, output_framerate, a_width, a_height, timestamps):
    """使用PTS操控编码视频，精确控制B帧时间戳"""
    ffmpeg_cmd, _ = check_ffmpeg()
    if not ffmpeg_cmd:
        return False

    try:
        sequence_dir = os.path.join(temp_dir, 'sequence')

        if not os.path.exists(sequence_dir):
            print("错误: 预排序图片目录不存在")
            return False

        # 第一步：创建带PTS的视频流
        frames_list_file = os.path.join(temp_dir, "frames_list.txt")
        with open(frames_list_file, 'w', encoding='utf-8') as f:
            for i, (frame_type, timestamp) in enumerate(zip(frame_sequence, timestamps)):
                frame_path = os.path.join(sequence_dir, f'frame_{i+1:06d}.png')
                if os.path.exists(frame_path):
                    # 转换Windows路径为FFmpeg兼容格式
                    ffmpeg_path = frame_path.replace('\\', '/')
                    f.write(f"file '{ffmpeg_path}'\n")
                    
                    # 为B帧设置特殊的PTS
                    if frame_type == 'B':
                        # B帧使用很小的时间增量，但包装在自然的变化中
                        f.write(f"duration 0.000001\n")  # 极短显示时间
                    else:
                        # I/P帧使用正常时长
                        next_ts = timestamps[i+1] if i < len(timestamps)-1 else timestamp + 1.0/output_framerate
                        duration = max(next_ts - timestamp, 0.000001)
                        f.write(f"duration {duration:.6f}\n")

        # 检查文件是否正确生成
        if not os.path.exists(frames_list_file):
            print("错误: 帧列表文件创建失败")
            return False
            
        # 检查帧列表文件内容
        with open(frames_list_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content.strip():
                print("错误: 帧列表文件为空")
                return False
        
        print(f"帧列表文件创建成功: {frames_list_file}")
        print(f"帧列表前几行内容:")
        with open(frames_list_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:6]):  # 显示前6行
                print(f"  {line.strip()}")
            if len(lines) > 6:
                print(f"  ... (共{len(lines)}行)")

        # 第二步：使用复杂的滤镜图来处理时间戳
        temp_video_path = os.path.join(temp_dir, "temp_video.mp4")
        
        # 使用setpts滤镜精确控制时间戳
        filter_complex = "setpts='N/({fps}*TB)'".format(fps=output_framerate)
        
        ffmpeg_cmd_list = [
            ffmpeg_cmd, '-y',
            '-f', 'concat',
            '-safe', '0',
            '-i', frames_list_file,
            '-vf', filter_complex,
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-crf', '23',
            '-pix_fmt', 'yuv420p',
            '-r', str(output_framerate),
            '-g', '60',  # GOP大小
            '-bf', '3',  # B帧数量
            '-b_strategy', '0',  # B帧策略
            '-movflags', '+faststart',
            '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
            temp_video_path
        ]

        print(f"执行FFmpeg命令:")
        print(f"  {' '.join(ffmpeg_cmd_list)}")
        
        result = subprocess.run(ffmpeg_cmd_list, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"FFmpeg编码失败: {result.stderr}")
            print(f"FFmpeg输出: {result.stdout}")
            
            # 尝试简化的方法
            print("尝试简化编码方法...")
            simple_cmd = [
                ffmpeg_cmd, '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', frames_list_file,
                '-c:v', 'libx264',
                '-preset', 'fast',
                '-crf', '23',
                '-pix_fmt', 'yuv420p',
                '-movflags', '+faststart',
                temp_video_path
            ]
            
            result = subprocess.run(simple_cmd, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                print(f"简化编码也失败: {result.stderr}")
                return False

        # 第三步：后处理，进一步调整时间戳
        final_temp_path = os.path.join(temp_dir, "final_temp.mp4")
        
        # 使用fps滤镜和复杂的时间戳处理
        post_process_cmd = [
            ffmpeg_cmd, '-y',
            '-i', temp_video_path,
            '-vf', f"fps={output_framerate}",
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-crf', '23',
            '-force_key_frames', 'expr:gte(t,n_forced*2)',  # 每2秒强制关键帧
            final_temp_path
        ]
        
        result2 = subprocess.run(post_process_cmd, capture_output=True, text=True, timeout=300)
        if result2.returncode != 0:
            print(f"后处理失败: {result2.stderr}")
            return False

        # 第四步：合并音频
        ffmpeg_audio_cmd = [
            ffmpeg_cmd, '-y',
            '-i', final_temp_path,
            '-i', video_a_path,
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-map_metadata', '0',
            '-shortest',
            output_path
        ]

        result_audio = subprocess.run(ffmpeg_audio_cmd, capture_output=True, text=True, timeout=300)
        if result_audio.returncode != 0:
            print(f"音频合并失败: {result_audio.stderr}")
            return False

        return True

    except Exception as e:
        print(f"编码失败: {e}")
        return False


def process_videos(video_a_path, video_b_path, output_path):
    """处理双视频合并"""
    # 获取视频信息
    info_a = get_video_info(video_a_path)
    info_b = get_video_info(video_b_path)

    if not info_a or not info_b:
        print("错误: 无法获取视频信息")
        return False

    # 创建临时目录
    import time
    temp_dir = f"temp_frames_{int(time.time())}"

    try:
        # 提取帧
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        if not extract_frames(video_a_path, frames_a_dir):
            return False

        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        if not extract_frames(video_b_path, frames_b_dir):
            return False

        # 创建自适应帧序列
        frame_sequence = create_adaptive_frame_sequence(
            info_a['total_frames'], info_b['total_frames'])

        if not frame_sequence:
            print("错误: 无法创建帧序列")
            return False

        # 按序列排列图片帧
        sequence_dir = os.path.join(temp_dir, 'sequence')
        if not arrange_frames_by_sequence(frames_a_dir, frames_b_dir, sequence_dir,
                                        frame_sequence,
                                        info_a['total_frames'], info_b['total_frames']):
            print("错误: 图片帧排列失败")
            return False

        # 计算并规范化输出帧率
        calculated_fps = len(frame_sequence) / info_a['duration']
        output_framerate = normalize_framerate(calculated_fps, info_a['fps'])

        # 生成隐蔽的B帧时间戳
        timestamps = generate_stealth_b_timestamps(frame_sequence, output_framerate)

        # 编码视频
        success = encode_video_with_pts_manipulation(frame_sequence, temp_dir, output_path, video_a_path,
                                                   output_framerate, info_a['width'], info_a['height'], timestamps)

        return success

    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass


def get_video_path(video_type):
    """获取视频路径的交互函数"""
    while True:
        try:
            user_input = input(f"拖入{video_type}视频或输入路径 (按Q退出): ").strip()

            if user_input.lower() == 'q':
                return None

            if user_input.startswith('"') and user_input.endswith('"'):
                user_input = user_input[1:-1]

            if not user_input:
                print("路径不能为空")
                continue

            if not os.path.exists(user_input):
                print("文件不存在")
                continue

            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v']
            file_ext = os.path.splitext(user_input)[1].lower()
            if file_ext not in video_extensions:
                print("不支持的文件格式")
                continue

            return user_input

        except KeyboardInterrupt:
            print("\n程序已退出")
            return None
        except Exception as e:
            print("输入错误，请重新输入")
            continue


def main():
    """主程序入口"""
    print("ABT - 双视频处理工具 (修复版)")
    print("修复时间戳异常问题，避免平台检测")

    # 获取A视频路径
    video_a_path = get_video_path("A")
    if not video_a_path:
        return

    # 获取B视频路径
    video_b_path = get_video_path("B")
    if not video_b_path:
        return

    # 生成输出文件路径
    a_dir = os.path.dirname(video_a_path)
    a_filename = os.path.splitext(os.path.basename(video_a_path))[0]
    output_path = os.path.join(a_dir, f"{a_filename}_natural.mp4")

    print("正在处理...")

    # 处理视频
    success = process_videos(video_a_path, video_b_path, output_path)

    if success:
        print(f"处理完成: {output_path}")
        print("使用自然时间戳，应该可以正常发布")
    else:
        print("处理失败")


if __name__ == "__main__":
    main()