#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术最终解决方案
使用精确的时间戳控制确保A帧100%完整性
"""

import os
import subprocess
import tempfile
import shutil
import time

class ABFinalSolution:
    """AB融帧最终解决方案"""
    
    def __init__(self):
        self.ffmpeg_cmd = 'ffmpeg'
        
    def create_frame_list_with_precise_timing(self, frames_a_dir, frames_b_dir, output_file):
        """创建精确时间戳的帧列表"""
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        print(f"创建精确时间戳帧列表: A帧{count_a}, B帧{count_b}")
        
        # 计算精确的帧时间 - 60fps以容纳1:1交替
        frame_duration = 1.0 / 60.0  # 60fps，每帧0.016667秒
        
        with open(output_file, 'w') as f:
            current_time = 0.0
            
            for i in range(count_a):
                # A帧
                a_frame_path = os.path.join(frames_a_dir, frames_a[i]).replace('\\', '/')
                f.write(f"file '{a_frame_path}'\n")
                f.write(f"duration {frame_duration:.6f}\n")
                current_time += frame_duration
                
                # B帧（循环使用）
                b_index = i % count_b
                b_frame_path = os.path.join(frames_b_dir, frames_b[b_index]).replace('\\', '/')
                f.write(f"file '{b_frame_path}'\n")
                f.write(f"duration {frame_duration:.6f}\n")
                current_time += frame_duration
            
            # 添加最后一帧的持续时间
            f.write(f"duration {frame_duration:.6f}\n")
        
        total_frames = count_a * 2
        total_duration = total_frames * frame_duration
        
        print(f"✓ 帧列表创建完成:")
        print(f"  总帧数: {total_frames}")
        print(f"  A帧数: {count_a}")
        print(f"  B帧数: {count_a}")
        print(f"  总时长: {total_duration:.3f}秒")
        print(f"  帧率: 60fps")
        
        return total_frames, total_duration
    
    def create_perfect_ab_video(self, video_a_path, video_b_path, output_path):
        """创建完美的AB融帧视频"""
        print("🎯 AB融帧技术最终解决方案")
        print("=" * 80)
        
        temp_dir = tempfile.mkdtemp(prefix='ab_final_')
        
        try:
            # 步骤1: 提取A视频帧
            print("\n📋 步骤1: 提取A视频帧")
            print("-" * 50)
            
            frames_a_dir = os.path.join(temp_dir, 'frames_a')
            os.makedirs(frames_a_dir, exist_ok=True)
            
            cmd_a = [
                self.ffmpeg_cmd, '-y', '-i', video_a_path,
                '-vsync', '0',
                f'{frames_a_dir}/frame_%06d.png'
            ]
            
            result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
            if result_a.returncode != 0:
                print(f"❌ A视频帧提取失败: {result_a.stderr}")
                return False
            
            frames_a = [f for f in os.listdir(frames_a_dir) if f.endswith('.png')]
            count_a = len(frames_a)
            print(f"✓ A视频帧提取完成: {count_a}帧")
            
            # 步骤2: 提取B视频帧
            print("\n📋 步骤2: 提取B视频帧")
            print("-" * 50)
            
            frames_b_dir = os.path.join(temp_dir, 'frames_b')
            os.makedirs(frames_b_dir, exist_ok=True)
            
            cmd_b = [
                self.ffmpeg_cmd, '-y', '-i', video_b_path,
                '-vsync', '0',
                f'{frames_b_dir}/frame_%06d.png'
            ]
            
            result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
            if result_b.returncode != 0:
                print(f"❌ B视频帧提取失败: {result_b.stderr}")
                return False
            
            frames_b = [f for f in os.listdir(frames_b_dir) if f.endswith('.png')]
            count_b = len(frames_b)
            print(f"✓ B视频帧提取完成: {count_b}帧")
            
            # 步骤3: 创建精确时间戳帧列表
            print("\n📋 步骤3: 创建精确时间戳帧列表")
            print("-" * 50)
            
            frame_list = os.path.join(temp_dir, 'frame_list.txt')
            total_frames, total_duration = self.create_frame_list_with_precise_timing(
                frames_a_dir, frames_b_dir, frame_list
            )
            
            # 步骤4: 提取音频
            print("\n📋 步骤4: 提取音频")
            print("-" * 50)
            
            audio_file = os.path.join(temp_dir, 'audio.aac')
            audio_cmd = [
                self.ffmpeg_cmd, '-y', '-i', video_a_path,
                '-vn', '-acodec', 'copy', audio_file
            ]
            
            audio_result = subprocess.run(audio_cmd, capture_output=True, text=True, timeout=60)
            has_audio = audio_result.returncode == 0
            
            if has_audio:
                print("✓ 音频提取成功")
            else:
                print("⚠️ 音频提取失败，将创建无音频视频")
            
            # 步骤5: 使用concat demuxer创建视频
            print("\n📋 步骤5: 创建最终AB融帧视频")
            print("-" * 50)
            
            # 先创建无音频版本
            temp_video = os.path.join(temp_dir, 'temp_video.mp4')
            
            concat_cmd = [
                self.ffmpeg_cmd, '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', frame_list,
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '18',
                '-pix_fmt', 'yuv420p',
                '-r', '60',  # 输出60fps
                '-vsync', 'cfr',
                '-avoid_negative_ts', 'make_zero',
                temp_video
            ]
            
            print("开始视频编码...")
            start_time = time.time()
            
            result = subprocess.run(concat_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                print(f"❌ 视频编码失败: {result.stderr}")
                return False
            
            encode_time = time.time() - start_time
            print(f"✓ 视频编码完成，耗时: {encode_time:.2f}秒")
            
            # 步骤6: 添加音频（如果有）
            if has_audio:
                print("\n📋 步骤6: 添加音频")
                print("-" * 50)
                
                final_cmd = [
                    self.ffmpeg_cmd, '-y',
                    '-i', temp_video,
                    '-i', audio_file,
                    '-c:v', 'copy',
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    '-ar', '44100',
                    '-ac', '2',
                    '-shortest',
                    '-metadata', 'comment=AB_FRAME_INTERLEAVING_V5.0_PERFECT_INTEGRITY',
                    '-metadata', 'frame_pattern=PERFECT_A1_B1_A2_B2_60FPS',
                    '-metadata', f'total_frames={total_frames}',
                    '-metadata', f'a_frames={count_a}',
                    '-metadata', f'a_integrity=100_percent_guaranteed',
                    output_path
                ]
                
                final_result = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
                
                if final_result.returncode != 0:
                    print(f"❌ 音频合并失败: {final_result.stderr}")
                    # 使用无音频版本
                    shutil.copy2(temp_video, output_path)
                else:
                    print("✓ 音频合并完成")
            else:
                # 直接使用无音频版本
                shutil.copy2(temp_video, output_path)
            
            # 显示结果
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024
                
                print(f"\n✅ AB融帧视频创建成功!")
                print(f"📁 输出文件: {os.path.basename(output_path)}")
                print(f"📊 文件大小: {file_size:.2f} MB")
                print(f"🎞️ 理论帧数: {total_frames}")
                print(f"🎯 A帧数: {count_a}")
                print(f"⏱️ 理论时长: {total_duration:.3f}秒")
                print(f"🎬 帧率: 60fps")
                
                return True
            else:
                print("❌ 输出文件未生成")
                return False
                
        except Exception as e:
            print(f"处理出错: {e}")
            return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"🧹 清理临时文件: {temp_dir}")
                except:
                    pass
    
    def verify_final_integrity(self, ab_video_path, original_a_path):
        """最终完整性验证"""
        print("\n🔍 最终A帧完整性验证")
        print("=" * 80)
        
        # 获取原始A视频帧数
        cmd_orig = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0',
            original_a_path
        ]
        
        result_orig = subprocess.run(cmd_orig, capture_output=True, text=True, timeout=30)
        if result_orig.returncode != 0:
            print("❌ 无法获取原始视频帧数")
            return False
        
        original_frames = int(result_orig.stdout.strip())
        
        # 获取AB视频总帧数
        cmd_ab = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0',
            ab_video_path
        ]
        
        result_ab = subprocess.run(cmd_ab, capture_output=True, text=True, timeout=30)
        if result_ab.returncode != 0:
            print("❌ 无法获取AB视频帧数")
            return False
        
        ab_total_frames = int(result_ab.stdout.strip())
        
        # 提取A帧进行验证
        temp_dir = tempfile.mkdtemp(prefix='verify_final_')
        try:
            extract_cmd = [
                self.ffmpeg_cmd, '-y', '-i', ab_video_path,
                '-vf', 'select=not(mod(n\\,2))',
                '-vsync', '0',
                f'{temp_dir}/a_frame_%06d.png'
            ]
            
            result = subprocess.run(extract_cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                extracted_files = [f for f in os.listdir(temp_dir) if f.endswith('.png')]
                extracted_frames = len(extracted_files)
                
                print(f"📊 验证结果:")
                print(f"  原始A视频帧数: {original_frames}")
                print(f"  AB视频总帧数: {ab_total_frames}")
                print(f"  提取的A帧数: {extracted_frames}")
                print(f"  理论A帧数: {ab_total_frames // 2}")
                print(f"  A帧完整性: {extracted_frames}/{original_frames} ({extracted_frames/original_frames*100:.1f}%)")
                
                integrity_perfect = extracted_frames == original_frames
                
                if integrity_perfect:
                    print("✅ A帧完整性验证通过！100%完整！")
                else:
                    print(f"❌ A帧不完整，缺失 {original_frames - extracted_frames} 帧")
                
                return integrity_perfect
            else:
                print(f"❌ A帧提取失败: {result.stderr}")
                return False
                
        finally:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)


def main():
    """主程序"""
    print("🎯 AB融帧技术最终解决方案")
    print("=" * 80)
    
    # 输入文件
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    # 输出文件
    output_path = "ab_final_perfect.mp4"
    
    solution = ABFinalSolution()
    
    # 创建完美的AB融帧视频
    success = solution.create_perfect_ab_video(video_a_path, video_b_path, output_path)
    
    if success:
        # 验证A帧完整性
        solution.verify_final_integrity(output_path, video_a_path)
        
        print(f"\n🎉 最终解决方案完成!")
        print(f"📁 输出文件: {output_path}")
        print(f"🎯 目标: A帧100%完整性")
    else:
        print("\n❌ 创建失败")


if __name__ == "__main__":
    main()
