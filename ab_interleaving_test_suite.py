#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术实用验证套件
专注于实际播放效果和用户体验验证

验证重点:
1. 实际播放质量验证
2. A帧完整性视觉检查
3. 音视频同步实际测试
4. 跨播放器兼容性验证
5. 用户体验评估
"""

import os
import sys
import time
import json
import tempfile
import shutil
import subprocess
from pathlib import Path

# 导入我们的工具包
from ab_frame_interleaving_toolkit import ABFrameInterleavingToolkit

class ABInterleavingTestSuite:
    """AB融帧技术实用验证套件"""

    def __init__(self):
        self.toolkit = ABFrameInterleavingToolkit()
        self.test_results = {}
        self.verification_data = {}

    def create_visual_comparison_frames(self, video_path, output_dir, frame_count=5):
        """创建视觉对比帧"""
        os.makedirs(output_dir, exist_ok=True)

        try:
            # 提取关键帧用于视觉对比
            cmd = [
                'ffmpeg', '-y', '-i', video_path,
                '-vf', f'select=eq(pict_type\\,I)',
                '-frames:v', str(frame_count),
                f'{output_dir}/keyframe_%03d.png'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                extracted_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
                return len(extracted_files)
            else:
                print(f"关键帧提取失败: {result.stderr}")
                return 0
        except Exception as e:
            print(f"视觉对比帧创建失败: {e}")
            return 0
    
    def log_test(self, test_name, result, details=""):
        """记录测试结果"""
        self.test_results[test_name] = {
            'result': result,
            'details': details,
            'timestamp': time.time()
        }
        
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def test_input_analysis(self, video_a_path, video_b_path):
        """测试1: 输入视频分析"""
        print("\n🧪 测试1: 输入视频分析")
        print("-" * 50)

        if not os.path.exists(video_a_path):
            self.log_test("输入文件检查", False, f"A视频不存在: {video_a_path}")
            return False

        if not os.path.exists(video_b_path):
            self.log_test("输入文件检查", False, f"B视频不存在: {video_b_path}")
            return False

        self.log_test("输入文件检查", True, "所有输入文件存在")

        # 使用工具包方法获取基本信息，添加异常处理
        try:
            info_a = self.toolkit.get_video_info(video_a_path)
            info_b = self.toolkit.get_video_info(video_b_path)

            if info_a and info_b:
                self.verification_data['original_a'] = info_a
                self.verification_data['original_b'] = info_b

                print(f"📺 视频A: {info_a['width']}x{info_a['height']}, {info_a['nb_frames']}帧, {info_a['duration']:.2f}s")
                print(f"📺 视频B: {info_b['width']}x{info_b['height']}, {info_b['nb_frames']}帧, {info_b['duration']:.2f}s")

                self.log_test("视频信息获取", True, f"A: {info_a['nb_frames']}帧, B: {info_b['nb_frames']}帧")
            else:
                # 如果工具包方法失败，使用简化的手动方法
                print("⚠️ 工具包方法失败，使用简化检查...")

                # 简单检查文件是否为有效视频
                cmd_a = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_a_path]
                cmd_b = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_b_path]

                result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=30)
                result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=30)

                if result_a.returncode == 0 and result_b.returncode == 0:
                    duration_a = float(result_a.stdout.strip()) if result_a.stdout.strip() else 0
                    duration_b = float(result_b.stdout.strip()) if result_b.stdout.strip() else 0

                    # 使用简化的信息
                    self.verification_data['original_a'] = {'duration': duration_a, 'nb_frames': 0, 'width': 0, 'height': 0}
                    self.verification_data['original_b'] = {'duration': duration_b, 'nb_frames': 0, 'width': 0, 'height': 0}

                    print(f"📺 视频A: 时长{duration_a:.2f}s")
                    print(f"📺 视频B: 时长{duration_b:.2f}s")

                    self.log_test("视频信息获取", True, f"A: {duration_a:.2f}s, B: {duration_b:.2f}s")
                else:
                    self.log_test("视频信息获取", False, "无法获取视频基本信息")
                    return False

        except Exception as e:
            print(f"获取视频信息失败: {e}")
            self.log_test("视频信息获取", False, f"异常: {e}")
            return False

        return True
    
    def test_ab_creation(self, video_a_path, video_b_path, output_path):
        """测试AB融帧视频创建"""
        print("\n🧪 测试2: AB融帧视频创建")
        print("-" * 50)
        
        start_time = time.time()
        success = self.toolkit.create_ab_interleaved_video(video_a_path, video_b_path, output_path)
        creation_time = time.time() - start_time
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / 1024 / 1024
            self.log_test("AB融帧视频创建", True, f"耗时: {creation_time:.2f}s, 大小: {file_size:.2f}MB")
            return True
        else:
            self.log_test("AB融帧视频创建", False, "创建失败")
            return False
    
    def test_a_frame_visual_integrity(self, ab_video_path, original_a_path, test_dir):
        """测试3: A帧视觉完整性验证"""
        print("\n🧪 测试3: A帧视觉完整性验证")
        print("-" * 50)

        # 创建对比目录
        comparison_dir = os.path.join(test_dir, 'visual_comparison')
        os.makedirs(comparison_dir, exist_ok=True)

        # 从AB视频提取A帧关键帧
        ab_frames_dir = os.path.join(comparison_dir, 'ab_extracted')
        ab_frame_count = self.create_visual_comparison_frames(ab_video_path, ab_frames_dir)

        # 从原始A视频提取关键帧
        original_frames_dir = os.path.join(comparison_dir, 'original_a')
        original_frame_count = self.create_visual_comparison_frames(original_a_path, original_frames_dir)

        if ab_frame_count == 0 or original_frame_count == 0:
            self.log_test("关键帧提取", False, "无法提取对比帧")
            return False

        print(f"📊 提取对比帧: AB视频{ab_frame_count}帧, 原始A视频{original_frame_count}帧")

        # 提供视觉验证指导
        print(f"\n👁️ 视觉验证指导:")
        print(f"请手动对比以下目录中的图片:")
        print(f"AB提取帧: {ab_frames_dir}")
        print(f"原始A帧: {original_frames_dir}")
        print(f"验证要点:")
        print(f"1. 图片内容是否一致")
        print(f"2. 画质是否保持")
        print(f"3. 颜色是否准确")

        # 自动检查文件大小差异（粗略指标）
        size_check_passed = True
        if ab_frame_count > 0 and original_frame_count > 0:
            ab_files = [f for f in os.listdir(ab_frames_dir) if f.endswith('.png')]
            orig_files = [f for f in os.listdir(original_frames_dir) if f.endswith('.png')]

            if ab_files and orig_files:
                ab_size = os.path.getsize(os.path.join(ab_frames_dir, ab_files[0]))
                orig_size = os.path.getsize(os.path.join(original_frames_dir, orig_files[0]))
                size_diff_ratio = abs(ab_size - orig_size) / orig_size

                size_check_passed = size_diff_ratio < 0.3  # 允许30%的大小差异
                print(f"📏 文件大小检查: AB帧{ab_size}字节, 原始{orig_size}字节, 差异{size_diff_ratio:.1%}")

        self.log_test("关键帧提取", True, f"AB:{ab_frame_count}帧, 原始:{original_frame_count}帧")
        self.log_test("文件大小检查", size_check_passed, "帧大小差异在合理范围内")

        return True
    
    def test_audio_video_sync_practical(self, ab_video_path, original_a_path):
        """测试4: 音视频同步实际验证"""
        print("\n🧪 测试4: 音视频同步实际验证")
        print("-" * 50)

        # 获取AB视频信息
        ab_info = self.toolkit.get_video_info(ab_video_path)
        original_info = self.toolkit.get_video_info(original_a_path)

        if not ab_info or not original_info:
            self.log_test("视频信息获取", False, "无法获取视频信息")
            return False

        # 检查时长匹配
        duration_diff = abs(ab_info['duration'] - original_info['duration'])
        duration_match = duration_diff < 0.5  # 允许0.5秒差异

        print(f"⏱️ 时长对比:")
        print(f"   AB视频时长: {ab_info['duration']:.3f}秒")
        print(f"   原始A时长: {original_info['duration']:.3f}秒")
        print(f"   时长差异: {duration_diff:.3f}秒")

        # 创建带时间戳的测试视频用于手动验证
        sync_test_video = "sync_test_with_timestamp.mp4"
        try:
            cmd = [
                'ffmpeg', '-y', '-i', ab_video_path,
                '-vf', 'drawtext=text=\'%{pts\\:hms}\':x=10:y=10:fontsize=30:fontcolor=yellow:box=1:boxcolor=black@0.5',
                '-c:a', 'copy',
                '-t', '10',  # 只生成前10秒用于测试
                sync_test_video
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"✅ 创建同步测试视频: {sync_test_video}")
                print(f"👁️ 手动验证指导:")
                print(f"   1. 播放 {sync_test_video}")
                print(f"   2. 观察视频左上角的时间戳")
                print(f"   3. 检查音频是否与视频内容同步")
                print(f"   4. 注意是否有音画不同步现象")

                sync_test_created = True
            else:
                print(f"⚠️ 同步测试视频创建失败: {result.stderr}")
                sync_test_created = False
        except Exception as e:
            print(f"⚠️ 同步测试视频创建出错: {e}")
            sync_test_created = False

        self.log_test("时长匹配", duration_match, f"差异{duration_diff:.3f}秒")
        self.log_test("同步测试视频", sync_test_created, "创建带时间戳的测试视频")

        return duration_match
    
    def test_a_frame_extraction_accuracy(self, ab_video_path, original_a_path, extract_dir):
        """测试5: A帧提取准确性验证"""
        print("\n🧪 测试5: A帧提取准确性验证")
        print("-" * 50)

        os.makedirs(extract_dir, exist_ok=True)

        # 提取A视频
        extracted_a = os.path.join(extract_dir, 'extracted_a_accuracy.mp4')
        success_a = self.toolkit.extract_hidden_video(ab_video_path, extracted_a, 'A')

        if not success_a or not os.path.exists(extracted_a):
            self.log_test("A视频提取", False, "提取失败")
            return False

        # 获取提取视频信息
        extracted_info = self.toolkit.get_video_info(extracted_a)
        original_info = self.toolkit.get_video_info(original_a_path)

        if not extracted_info or not original_info:
            self.log_test("视频信息获取", False, "无法获取视频信息")
            return False

        # 对比关键指标
        frame_accuracy = abs(extracted_info['nb_frames'] - original_info['nb_frames']) <= 2
        duration_accuracy = abs(extracted_info['duration'] - original_info['duration']) < 1.0
        resolution_match = (extracted_info['width'] == original_info['width'] and
                          extracted_info['height'] == original_info['height'])

        print(f"📊 提取准确性对比:")
        print(f"   帧数: 提取{extracted_info['nb_frames']} vs 原始{original_info['nb_frames']}")
        print(f"   时长: 提取{extracted_info['duration']:.2f}s vs 原始{original_info['duration']:.2f}s")
        print(f"   分辨率: 提取{extracted_info['width']}x{extracted_info['height']} vs 原始{original_info['width']}x{original_info['height']}")

        # 创建并排对比视频
        comparison_video = os.path.join(extract_dir, 'side_by_side_comparison.mp4')
        try:
            cmd = [
                'ffmpeg', '-y',
                '-i', extracted_a,
                '-i', original_a_path,
                '-filter_complex', '[0:v][1:v]hstack=inputs=2[v]',
                '-map', '[v]',
                '-t', '10',  # 只对比前10秒
                '-c:v', 'libx264', '-crf', '18',
                comparison_video
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                print(f"✅ 创建并排对比视频: {comparison_video}")
                print(f"👁️ 视觉对比指导:")
                print(f"   播放 {comparison_video} 进行左右对比")
                print(f"   左侧: 从AB视频提取的A视频")
                print(f"   右侧: 原始A视频")
                print(f"   检查两侧是否完全一致")
                comparison_created = True
            else:
                print(f"⚠️ 对比视频创建失败")
                comparison_created = False
        except Exception as e:
            print(f"⚠️ 对比视频创建出错: {e}")
            comparison_created = False

        self.log_test("A视频提取", True, f"提取{extracted_info['nb_frames']}帧")
        self.log_test("帧数准确性", frame_accuracy, f"帧数差异: {abs(extracted_info['nb_frames'] - original_info['nb_frames'])}")
        self.log_test("时长准确性", duration_accuracy, f"时长差异: {abs(extracted_info['duration'] - original_info['duration']):.3f}s")
        self.log_test("分辨率匹配", resolution_match, "分辨率是否匹配")
        self.log_test("对比视频创建", comparison_created, "并排对比视频")

        return frame_accuracy and duration_accuracy and resolution_match
    
    def test_player_compatibility(self, ab_video_path):
        """测试播放器兼容性"""
        print("\n🧪 测试6: 播放器兼容性")
        print("-" * 50)
        
        compatibility = self.toolkit.verify_compatibility(ab_video_path)
        self.log_test("播放器兼容性", compatibility, "视频符合标准播放器要求")
        
        return compatibility
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 测试报告")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['result'])
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅" if result['result'] else "❌"
            print(f"{status} {test_name}: {result['details']}")
        
        # 保存报告到文件
        report_file = f"ab_interleaving_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        return passed_tests == total_tests
    
    def run_practical_verification_suite(self, video_a_path, video_b_path):
        """运行实用验证套件"""
        print("🧪 AB融帧技术实用验证套件")
        print("专注于实际播放效果和用户体验验证")
        print("=" * 80)

        # 准备测试环境
        test_dir = tempfile.mkdtemp(prefix='ab_practical_test_')
        ab_output = os.path.join(test_dir, 'ab_practical_test.mp4')
        extract_dir = os.path.join(test_dir, 'extracted')

        try:
            # 执行实用验证序列
            print("🎯 开始实用验证流程...")

            test_1 = self.test_input_analysis(video_a_path, video_b_path)

            if test_1:
                test_2 = self.test_ab_creation(video_a_path, video_b_path, ab_output)

                if test_2:
                    test_3 = self.test_a_frame_visual_integrity(ab_output, video_a_path, test_dir)
                    test_4 = self.test_audio_video_sync_practical(ab_output, video_a_path)
                    test_5 = self.test_a_frame_extraction_accuracy(ab_output, video_a_path, extract_dir)
                    test_6 = self.test_detection_capability(ab_output)

                    # 提供实际验证指导
                    print(f"\n🎬 实际播放验证指导:")
                    print(f"=" * 50)
                    print(f"1. 主要测试文件: {ab_output}")
                    print(f"2. 请在以下播放器中测试:")
                    print(f"   - VLC Media Player")
                    print(f"   - Windows Media Player")
                    print(f"   - PotPlayer")
                    print(f"3. 验证要点:")
                    print(f"   ✓ 是否只显示A视频内容（e.mp4的内容）")
                    print(f"   ✓ 是否看不到B视频内容（IMG_1915.MP4的内容）")
                    print(f"   ✓ 音频是否与视频同步")
                    print(f"   ✓ 播放是否流畅无卡顿")
                    print(f"4. 对比文件:")
                    print(f"   - 原始A视频: {video_a_path}")
                    print(f"   - 提取的A视频: {os.path.join(extract_dir, 'extracted_a_accuracy.mp4')}")

                else:
                    print("⚠️ AB视频创建失败，跳过后续测试")
            else:
                print("⚠️ 输入分析失败，终止测试")

            # 生成实用验证报告
            all_passed = self.generate_practical_report(test_dir)

            if all_passed:
                print("\n🎉 实用验证完成！请进行手动播放测试确认最终效果！")
            else:
                print("\n⚠️ 部分验证失败，请检查实现细节")

            return all_passed

        finally:
            # 保留测试文件供手动验证
            print(f"\n📁 测试文件保留在: {test_dir}")
            print(f"请手动验证后删除该目录")


    def test_detection_capability(self, ab_video_path):
        """测试6: AB融帧检测能力"""
        print("\n🧪 测试6: AB融帧检测能力")
        print("-" * 50)

        detected = self.toolkit.detect_ab_interleaving(ab_video_path)
        self.log_test("AB融帧检测", detected, "检测算法能够识别AB融帧技术")

        return detected

    def generate_practical_report(self, test_dir):
        """生成实用验证报告"""
        print("\n📊 实用验证报告")
        print("=" * 80)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['result'])

        print(f"总验证项: {total_tests}")
        print(f"通过验证: {passed_tests}")
        print(f"失败验证: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")

        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅" if result['result'] else "❌"
            print(f"{status} {test_name}: {result['details']}")

        # 保存报告到文件
        report_file = os.path.join(test_dir, f"practical_verification_report_{int(time.time())}.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_results': self.test_results,
                'verification_data': self.verification_data,
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'pass_rate': (passed_tests/total_tests)*100
                }
            }, f, indent=2, ensure_ascii=False)

        print(f"\n📄 详细报告已保存: {report_file}")

        return passed_tests == total_tests


def main():
    """主程序"""
    print("🧪 AB融帧技术实用验证套件")
    print("=" * 60)

    # 使用实际的测试文件
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"

    # 检查测试文件
    if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
        print("⚠️ 测试文件不存在")
        print(f"请确认文件: {video_a_path}")
        print(f"请确认文件: {video_b_path}")
        return

    print(f"📁 使用测试文件:")
    print(f"   A视频（主视频）: {os.path.basename(video_a_path)}")
    print(f"   B视频（隐藏视频）: {os.path.basename(video_b_path)}")

    # 运行实用验证套件
    test_suite = ABInterleavingTestSuite()
    success = test_suite.run_practical_verification_suite(video_a_path, video_b_path)

    if success:
        print("\n✅ 自动验证完成！")
        print("🎬 请继续进行手动播放测试以确认最终效果")
    else:
        print("\n❌ 验证发现问题，需要进一步优化")


if __name__ == "__main__":
    main()
