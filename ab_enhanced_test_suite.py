#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术增强测试套件
专门验证A帧完整性和音视频同步的标准1:1交替实现
"""

import os
import time
import subprocess
import json
import tempfile
import shutil
from ab_frame_interleaving_toolkit import ABFrameInterleavingToolkit

class ABEnhancedTestSuite:
    """AB融帧技术增强测试套件"""
    
    def __init__(self):
        self.toolkit = ABFrameInterleavingToolkit()
        self.test_results = {}
        
    def log_test(self, test_name, result, details=""):
        """记录测试结果"""
        self.test_results[test_name] = {
            'result': result,
            'details': details,
            'timestamp': time.time()
        }
        
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def get_detailed_video_info(self, video_path):
        """获取详细的视频信息"""
        try:
            # 使用工具包的方法获取基本信息
            basic_info = self.toolkit.get_video_info(video_path)
            if not basic_info:
                return None

            # 检查音频流
            audio_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'a', '-show_entries', 'stream=codec_name,sample_rate,channels', '-of', 'csv=p=0', video_path]
            audio_result = subprocess.run(audio_cmd, capture_output=True, text=True, timeout=30)

            has_audio = False
            audio_info = None
            if audio_result.returncode == 0 and audio_result.stdout.strip():
                has_audio = True
                audio_parts = audio_result.stdout.strip().split(',')
                if len(audio_parts) >= 3:
                    audio_info = {
                        'codec_name': audio_parts[0],
                        'sample_rate': audio_parts[1],
                        'channels': audio_parts[2]
                    }

            return {
                'video': {
                    'width': basic_info['width'],
                    'height': basic_info['height'],
                    'nb_frames': basic_info['nb_frames'],
                    'codec_name': basic_info['codec_name']
                },
                'audio': audio_info if has_audio else None,
                'format': {
                    'duration': str(basic_info['duration'])
                }
            }
        except Exception as e:
            print(f"获取视频信息失败: {e}")

        return None
    
    def test_input_video_analysis(self, video_a_path, video_b_path):
        """测试1: 输入视频分析"""
        print("\n🧪 测试1: 输入视频详细分析")
        print("-" * 50)
        
        # 分析视频A
        info_a = self.get_detailed_video_info(video_a_path)
        if not info_a or not info_a['video']:
            self.log_test("视频A分析", False, "无法获取视频A信息")
            return False
        
        # 分析视频B
        info_b = self.get_detailed_video_info(video_b_path)
        if not info_b or not info_b['video']:
            self.log_test("视频B分析", False, "无法获取视频B信息")
            return False
        
        # 记录详细信息
        v_a = info_a['video']
        v_b = info_b['video']
        
        print(f"📺 视频A: {v_a['width']}x{v_a['height']}, {v_a['nb_frames']}帧, {float(info_a['format']['duration']):.3f}s")
        print(f"📺 视频B: {v_b['width']}x{v_b['height']}, {v_b['nb_frames']}帧, {float(info_b['format']['duration']):.3f}s")
        
        if info_a['audio']:
            print(f"🔊 视频A音频: {info_a['audio']['codec_name']}, {info_a['audio'].get('sample_rate', 'N/A')}Hz")
        if info_b['audio']:
            print(f"🔊 视频B音频: {info_b['audio']['codec_name']}, {info_b['audio'].get('sample_rate', 'N/A')}Hz")
        
        self.log_test("输入视频分析", True, f"A:{v_a['nb_frames']}帧, B:{v_b['nb_frames']}帧")
        
        # 保存基准数据
        self.baseline_data = {
            'a_frames': int(v_a['nb_frames']),
            'b_frames': int(v_b['nb_frames']),
            'a_duration': float(info_a['format']['duration']),
            'b_duration': float(info_b['format']['duration']),
            'a_has_audio': info_a['audio'] is not None,
            'b_has_audio': info_b['audio'] is not None
        }
        
        return True
    
    def test_ab_creation_with_integrity(self, video_a_path, video_b_path, output_path):
        """测试2: AB融帧创建（确保A帧完整性）"""
        print("\n🧪 测试2: AB融帧创建（A帧完整性保证）")
        print("-" * 50)
        
        start_time = time.time()
        success = self.toolkit.create_ab_interleaved_video(
            video_a_path=video_a_path,
            video_b_path=video_b_path,
            output_path=output_path,
            resolution='vertical_hd'
        )
        creation_time = time.time() - start_time
        
        if not success or not os.path.exists(output_path):
            self.log_test("AB融帧创建", False, "创建失败")
            return False
        
        # 分析输出视频
        output_info = self.get_detailed_video_info(output_path)
        if not output_info or not output_info['video']:
            self.log_test("AB融帧创建", False, "无法分析输出视频")
            return False
        
        v_out = output_info['video']
        f_out = output_info['format']
        
        file_size = os.path.getsize(output_path) / 1024 / 1024
        
        print(f"✅ AB融帧视频创建成功")
        print(f"📁 文件大小: {file_size:.2f} MB")
        print(f"⏱️ 创建耗时: {creation_time:.2f} 秒")
        print(f"📺 输出规格: {v_out['width']}x{v_out['height']}, {v_out['nb_frames']}帧, {float(f_out['duration']):.3f}s")
        
        # 验证帧数逻辑
        expected_total_frames = self.baseline_data['a_frames'] * 2  # A帧 + B帧（循环）
        actual_frames = int(v_out['nb_frames'])
        
        frame_integrity = abs(actual_frames - expected_total_frames) <= 2  # 允许2帧误差
        
        self.log_test("AB融帧创建", True, f"输出{actual_frames}帧, 预期{expected_total_frames}帧")
        self.log_test("帧数完整性", frame_integrity, f"帧数差异: {abs(actual_frames - expected_total_frames)}")
        
        # 保存输出数据
        self.output_data = {
            'total_frames': actual_frames,
            'duration': float(f_out['duration']),
            'has_audio': output_info['audio'] is not None,
            'file_size_mb': file_size
        }
        
        return True
    
    def test_audio_video_sync(self, ab_video_path):
        """测试3: 音视频同步验证"""
        print("\n🧪 测试3: 音视频同步验证")
        print("-" * 50)
        
        info = self.get_detailed_video_info(ab_video_path)
        if not info:
            self.log_test("音视频同步检查", False, "无法获取视频信息")
            return False
        
        if not info['audio']:
            self.log_test("音视频同步检查", False, "输出视频无音频")
            return False
        
        video_duration = float(info['format']['duration'])
        audio_duration = float(info['audio'].get('duration', 0))
        
        sync_diff = abs(video_duration - audio_duration)
        sync_good = sync_diff < 0.1  # 小于0.1秒认为同步良好
        
        print(f"📺 视频时长: {video_duration:.3f}秒")
        print(f"🔊 音频时长: {audio_duration:.3f}秒")
        print(f"🎯 同步差异: {sync_diff:.3f}秒")
        
        if sync_good:
            print("✅ 音视频同步良好")
        else:
            print("⚠️ 音视频可能不同步")
        
        self.log_test("音视频同步检查", sync_good, f"差异{sync_diff:.3f}秒")
        
        return sync_good
    
    def test_a_frame_integrity_extraction(self, ab_video_path, extract_dir):
        """测试4: A帧完整性提取验证"""
        print("\n🧪 测试4: A帧完整性提取验证")
        print("-" * 50)
        
        os.makedirs(extract_dir, exist_ok=True)
        
        # 提取A视频
        extracted_a = os.path.join(extract_dir, 'extracted_a_integrity.mp4')
        success_a = self.toolkit.extract_hidden_video(ab_video_path, extracted_a, 'A')
        
        if not success_a or not os.path.exists(extracted_a):
            self.log_test("A视频提取", False, "提取失败")
            return False
        
        # 分析提取的A视频
        extracted_info = self.get_detailed_video_info(extracted_a)
        if not extracted_info or not extracted_info['video']:
            self.log_test("A视频提取", False, "无法分析提取的A视频")
            return False
        
        extracted_frames = int(extracted_info['video']['nb_frames'])
        extracted_duration = float(extracted_info['format']['duration'])
        
        # 验证A帧完整性
        # 由于1:1交替，提取的A帧应该是原始A帧数量
        original_a_frames = self.baseline_data['a_frames']
        frame_integrity = abs(extracted_frames - original_a_frames) <= 1  # 允许1帧误差
        
        # 验证时长合理性
        duration_integrity = abs(extracted_duration - self.baseline_data['a_duration']) < 1.0  # 允许1秒误差
        
        print(f"📺 提取的A视频: {extracted_frames}帧, {extracted_duration:.3f}s")
        print(f"📊 原始A视频: {original_a_frames}帧, {self.baseline_data['a_duration']:.3f}s")
        print(f"🎯 帧数差异: {abs(extracted_frames - original_a_frames)}")
        print(f"🎯 时长差异: {abs(extracted_duration - self.baseline_data['a_duration']):.3f}s")
        
        self.log_test("A视频提取", True, f"提取{extracted_frames}帧")
        self.log_test("A帧完整性", frame_integrity, f"帧数匹配度: {extracted_frames}/{original_a_frames}")
        self.log_test("A视频时长", duration_integrity, f"时长差异: {abs(extracted_duration - self.baseline_data['a_duration']):.3f}s")
        
        return frame_integrity and duration_integrity
    
    def test_b_frame_extraction(self, ab_video_path, extract_dir):
        """测试5: B帧提取验证"""
        print("\n🧪 测试5: B帧提取验证")
        print("-" * 50)
        
        # 提取B视频
        extracted_b = os.path.join(extract_dir, 'extracted_b_integrity.mp4')
        success_b = self.toolkit.extract_hidden_video(ab_video_path, extracted_b, 'B')
        
        if not success_b or not os.path.exists(extracted_b):
            self.log_test("B视频提取", False, "提取失败")
            return False
        
        # 分析提取的B视频
        extracted_info = self.get_detailed_video_info(extracted_b)
        if not extracted_info or not extracted_info['video']:
            self.log_test("B视频提取", False, "无法分析提取的B视频")
            return False
        
        extracted_frames = int(extracted_info['video']['nb_frames'])
        extracted_duration = float(extracted_info['format']['duration'])
        
        print(f"📺 提取的B视频: {extracted_frames}帧, {extracted_duration:.3f}s")
        
        self.log_test("B视频提取", True, f"提取{extracted_frames}帧")

        return True

    def test_ab_detection_capability(self, ab_video_path):
        """测试6: AB融帧检测能力"""
        print("\n🧪 测试6: AB融帧检测能力")
        print("-" * 50)

        detected = self.toolkit.detect_ab_interleaving(ab_video_path)

        self.log_test("AB融帧检测", detected, "检测算法识别AB融帧技术")

        return detected

    def test_frame_pattern_analysis(self, ab_video_path):
        """测试7: 帧模式分析"""
        print("\n🧪 测试7: 帧模式分析（1:1交替验证）")
        print("-" * 50)

        try:
            # 分析前20帧的模式
            cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0',
                   '-show_entries', 'frame=n,pkt_pts_time', '-of', 'csv=p=0',
                   '-read_intervals', '%+#20', ab_video_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                frame_intervals = []

                prev_pts = None
                for line in lines:
                    if line and ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 2:
                            try:
                                pts = float(parts[1])
                                if prev_pts is not None:
                                    interval = pts - prev_pts
                                    frame_intervals.append(interval)
                                prev_pts = pts
                            except:
                                continue

                if frame_intervals:
                    avg_interval = sum(frame_intervals) / len(frame_intervals)
                    interval_consistency = all(abs(interval - avg_interval) < 0.001 for interval in frame_intervals)

                    print(f"📊 分析了{len(frame_intervals)}个帧间隔")
                    print(f"📈 平均帧间隔: {avg_interval:.6f}秒")
                    print(f"🎯 预期帧间隔: 0.033333秒 (30fps)")
                    print(f"📏 间隔一致性: {'良好' if interval_consistency else '不稳定'}")

                    # 验证是否接近30fps
                    fps_match = abs(avg_interval - 0.033333) < 0.001

                    self.log_test("帧间隔一致性", interval_consistency, f"平均间隔{avg_interval:.6f}s")
                    self.log_test("30fps帧率匹配", fps_match, f"间隔差异{abs(avg_interval - 0.033333):.6f}s")

                    return interval_consistency and fps_match

        except Exception as e:
            print(f"帧模式分析失败: {e}")

        self.log_test("帧模式分析", False, "分析失败")
        return False

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        print("\n📊 综合测试报告")
        print("=" * 80)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['result'])

        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")

        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅" if result['result'] else "❌"
            print(f"{status} {test_name}: {result['details']}")

        # 关键指标总结
        print(f"\n🎯 关键指标总结:")
        if hasattr(self, 'baseline_data') and hasattr(self, 'output_data'):
            print(f"原始A帧数: {self.baseline_data['a_frames']}")
            print(f"输出总帧数: {self.output_data['total_frames']}")
            print(f"预期总帧数: {self.baseline_data['a_frames'] * 2}")
            print(f"音频包含: {'是' if self.output_data['has_audio'] else '否'}")
            print(f"文件大小: {self.output_data['file_size_mb']:.2f} MB")

        # 保存详细报告
        report_file = f"ab_enhanced_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_results': self.test_results,
                'baseline_data': getattr(self, 'baseline_data', {}),
                'output_data': getattr(self, 'output_data', {}),
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'pass_rate': (passed_tests/total_tests)*100
                }
            }, f, indent=2, ensure_ascii=False)

        print(f"\n📄 详细报告已保存: {report_file}")

        return passed_tests == total_tests

    def run_enhanced_test_suite(self, video_a_path, video_b_path):
        """运行增强测试套件"""
        print("🧪 AB融帧技术增强测试套件")
        print("专门验证A帧完整性和音视频同步的标准1:1交替实现")
        print("=" * 80)

        # 准备测试环境
        test_dir = tempfile.mkdtemp(prefix='ab_enhanced_test_')
        ab_output = os.path.join(test_dir, 'ab_standard_1to1.mp4')
        extract_dir = os.path.join(test_dir, 'extracted')

        try:
            # 执行测试序列
            test_1 = self.test_input_video_analysis(video_a_path, video_b_path)

            if test_1:
                test_2 = self.test_ab_creation_with_integrity(video_a_path, video_b_path, ab_output)

                if test_2:
                    test_3 = self.test_audio_video_sync(ab_output)
                    test_4 = self.test_a_frame_integrity_extraction(ab_output, extract_dir)
                    test_5 = self.test_b_frame_extraction(ab_output, extract_dir)
                    test_6 = self.test_ab_detection_capability(ab_output)
                    test_7 = self.test_frame_pattern_analysis(ab_output)
                else:
                    print("⚠️ AB视频创建失败，跳过后续测试")
            else:
                print("⚠️ 输入视频分析失败，终止测试")

            # 生成综合报告
            all_passed = self.generate_comprehensive_report()

            if all_passed:
                print("\n🎉 所有测试通过！标准1:1 AB融帧技术验证成功！")
                print("✅ A帧完整性得到保证")
                print("✅ 音视频同步良好")
                print("✅ 1:1帧交替模式正常工作")
            else:
                print("\n⚠️ 部分测试失败，请检查实现细节")

            return all_passed

        finally:
            # 清理测试环境
            if os.path.exists(test_dir):
                try:
                    shutil.rmtree(test_dir)
                    print(f"🧹 清理测试环境: {test_dir}")
                except:
                    print(f"⚠️ 无法清理测试目录: {test_dir}")


def main():
    """主程序"""
    print("🧪 AB融帧技术增强测试套件")
    print("=" * 60)

    # 测试文件路径
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"

    # 检查测试文件
    if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
        print("⚠️ 测试文件不存在")
        print(f"请确认文件存在: {video_a_path}")
        print(f"请确认文件存在: {video_b_path}")
        return

    # 运行增强测试套件
    test_suite = ABEnhancedTestSuite()
    success = test_suite.run_enhanced_test_suite(video_a_path, video_b_path)

    if success:
        print("\n✅ 测试完成：标准1:1 AB融帧技术验证成功！")
    else:
        print("\n❌ 测试完成：发现问题，需要进一步优化")


if __name__ == "__main__":
    main()
