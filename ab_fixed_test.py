#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术修复版本测试脚本
验证闪烁问题和音频问题的修复效果
"""

import os
import time
import subprocess
from ab_frame_interleaving_toolkit import ABFrameInterleavingToolkit

def check_audio_streams(video_path):
    """检查视频的音频流数量"""
    try:
        cmd = ['ffprobe', '-v', 'error', '-select_streams', 'a', '-show_entries', 'stream=index', '-of', 'csv=p=0', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            audio_streams = result.stdout.strip().split('\n') if result.stdout.strip() else []
            return len([s for s in audio_streams if s])
        return 0
    except:
        return 0

def analyze_frame_timing(video_path):
    """分析视频帧时间，检查是否有异常短的帧"""
    try:
        cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', 
               '-show_entries', 'frame=pkt_duration_time', '-of', 'csv=p=0', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            durations = []
            for line in result.stdout.strip().split('\n'):
                if line and line != 'pkt_duration_time':
                    try:
                        duration = float(line)
                        durations.append(duration)
                    except:
                        continue
            
            if durations:
                avg_duration = sum(durations) / len(durations)
                short_frames = [d for d in durations if d < avg_duration * 0.1]  # 少于平均时长10%的帧
                return {
                    'total_frames': len(durations),
                    'avg_duration': avg_duration,
                    'short_frames_count': len(short_frames),
                    'short_frames_ratio': len(short_frames) / len(durations) if durations else 0
                }
    except Exception as e:
        print(f"帧时间分析失败: {e}")
    
    return None

def main():
    print("🔧 AB融帧技术修复版本测试")
    print("=" * 80)
    
    # 输入文件路径
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    # 输出文件路径
    fixed_output_path = "ab_interleaved_fixed.mp4"
    extracted_a_fixed = "extracted_a_fixed.mp4"
    extracted_b_fixed = "extracted_b_fixed.mp4"
    
    # 初始化工具包
    print("🔧 初始化修复版AB融帧工具包...")
    toolkit = ABFrameInterleavingToolkit()
    
    try:
        # 步骤1: 检查输入文件音频
        print("\n📋 步骤1: 检查输入文件音频")
        print("-" * 50)
        
        audio_a = check_audio_streams(video_a_path)
        audio_b = check_audio_streams(video_b_path)
        
        print(f"视频A音频流: {audio_a}个")
        print(f"视频B音频流: {audio_b}个")
        
        # 步骤2: 创建修复版AB融帧视频
        print("\n🎬 步骤2: 创建修复版AB融帧视频")
        print("-" * 50)
        
        start_time = time.time()
        success = toolkit.create_ab_interleaved_video(
            video_a_path=video_a_path,
            video_b_path=video_b_path,
            output_path=fixed_output_path,
            resolution='vertical_hd'
        )
        creation_time = time.time() - start_time
        
        if success and os.path.exists(fixed_output_path):
            file_size = os.path.getsize(fixed_output_path) / 1024 / 1024
            print(f"✅ 修复版AB融帧视频创建成功!")
            print(f"📁 输出文件: {fixed_output_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            print(f"⏱️ 创建耗时: {creation_time:.2f} 秒")
            
            # 检查输出视频音频
            output_audio = check_audio_streams(fixed_output_path)
            print(f"🔊 输出视频音频流: {output_audio}个")
            
            if output_audio > 0:
                print("✅ 音频问题已修复!")
            else:
                print("❌ 音频问题仍然存在")
            
        else:
            print("❌ 修复版AB融帧视频创建失败")
            return False
        
        # 步骤3: 分析帧时间（检查闪烁修复）
        print("\n🔍 步骤3: 分析帧时间（检查闪烁修复）")
        print("-" * 50)
        
        frame_analysis = analyze_frame_timing(fixed_output_path)
        if frame_analysis:
            print(f"📊 帧时间分析:")
            print(f"   总帧数: {frame_analysis['total_frames']}")
            print(f"   平均帧时长: {frame_analysis['avg_duration']:.6f}秒")
            print(f"   短帧数量: {frame_analysis['short_frames_count']}")
            print(f"   短帧比例: {frame_analysis['short_frames_ratio']:.2%}")
            
            if frame_analysis['short_frames_ratio'] > 0.4:  # 如果超过40%是短帧
                print("✅ 检测到隐藏帧结构，闪烁问题应该已修复")
            else:
                print("⚠️ 帧时间结构可能需要进一步优化")
        
        # 步骤4: 检测AB融帧技术
        print("\n🔍 步骤4: 检测修复版AB融帧技术")
        print("-" * 50)
        
        detected = toolkit.detect_ab_interleaving(fixed_output_path)
        if detected:
            print("✅ AB融帧技术检测成功!")
        else:
            print("❌ 未检测到AB融帧技术")
        
        # 步骤5: 提取测试（验证音频保持）
        print("\n🎯 步骤5: 提取测试（验证音频保持）")
        print("-" * 50)
        
        # 提取A视频
        success_a = toolkit.extract_hidden_video(fixed_output_path, extracted_a_fixed, 'A')
        # 提取B视频
        success_b = toolkit.extract_hidden_video(fixed_output_path, extracted_b_fixed, 'B')
        
        if success_a and success_b:
            print("✅ 视频提取成功!")
            
            # 检查提取视频的音频
            extracted_a_audio = check_audio_streams(extracted_a_fixed)
            extracted_b_audio = check_audio_streams(extracted_b_fixed)
            
            print(f"🔊 提取的A视频音频流: {extracted_a_audio}个")
            print(f"🔊 提取的B视频音频流: {extracted_b_audio}个")
            
            if extracted_a_audio > 0 and extracted_b_audio > 0:
                print("✅ 提取视频音频保持完整!")
            else:
                print("⚠️ 部分提取视频缺少音频")
        
        # 步骤6: 对比测试结果
        print("\n📊 步骤6: 修复效果对比")
        print("-" * 50)
        
        # 对比原版本和修复版本
        original_file = "ab_interleaved_test_output.mp4"
        if os.path.exists(original_file):
            original_audio = check_audio_streams(original_file)
            fixed_audio = check_audio_streams(fixed_output_path)
            
            print(f"原版本音频流: {original_audio}个")
            print(f"修复版音频流: {fixed_audio}个")
            
            if fixed_audio > original_audio:
                print("✅ 音频修复成功!")
            
            # 文件大小对比
            original_size = os.path.getsize(original_file) / 1024 / 1024
            fixed_size = os.path.getsize(fixed_output_path) / 1024 / 1024
            
            print(f"原版本大小: {original_size:.2f} MB")
            print(f"修复版大小: {fixed_size:.2f} MB")
            print(f"大小差异: {fixed_size - original_size:.2f} MB")
        
        print("\n🎉 修复测试完成!")
        print("=" * 80)
        
        # 总结修复效果
        print("📋 修复效果总结:")
        print("1. 音频问题修复: ✅ 已修复" if output_audio > 0 else "1. 音频问题修复: ❌ 仍有问题")
        print("2. 帧时间优化: ✅ 已优化" if frame_analysis and frame_analysis['short_frames_ratio'] > 0.3 else "2. 帧时间优化: ⚠️ 需要验证")
        print("3. AB融帧功能: ✅ 正常工作" if detected else "3. AB融帧功能: ❌ 有问题")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
        return False

if __name__ == "__main__":
    main()
