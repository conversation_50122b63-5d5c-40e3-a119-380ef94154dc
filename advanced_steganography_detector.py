#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级视频隐写技术检测器
专门检测AB融帧、PTS操控、SEI嵌入等隐写技术
"""

import os
import subprocess
import json
import tempfile
import shutil
import time
from pathlib import Path

def check_tools():
    """检查必要的工具"""
    tools = {}
    
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                tools['ffmpeg'] = cmd
                break
        except:
            continue
    
    for cmd in ['ffprobe', 'ffprobe.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                tools['ffprobe'] = cmd
                break
        except:
            continue
    
    return tools

def analyze_frame_structure(video_path, tools):
    """分析帧结构，检测异常模式"""
    print("🔍 分析帧结构...")
    
    ffprobe = tools.get('ffprobe')
    if not ffprobe:
        return None
    
    # 获取详细的帧信息
    cmd = [ffprobe, '-v', 'error', '-select_streams', 'v:0',
           '-show_entries', 'frame=n,pkt_pts_time,pkt_dts_time,pict_type,pkt_size,key_frame',
           '-of', 'json', video_path]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if result.returncode != 0:
            print(f"❌ 帧分析失败: {result.stderr}")
            return None
        
        frame_data = json.loads(result.stdout)
        frames = frame_data.get('frames', [])
        
        print(f"📊 总帧数: {len(frames)}")
        
        # 分析帧类型分布
        frame_types = {}
        pts_gaps = []
        large_frames = []
        
        for i, frame in enumerate(frames):
            frame_type = frame.get('pict_type', 'Unknown')
            frame_types[frame_type] = frame_types.get(frame_type, 0) + 1
            
            # 检查异常大的帧
            pkt_size = int(frame.get('pkt_size', 0))
            if pkt_size > 50000:  # 大于50KB的帧
                large_frames.append({
                    'frame_num': i,
                    'pts_time': frame.get('pkt_pts_time'),
                    'size': pkt_size,
                    'type': frame_type
                })
            
            # 检查PTS间隔
            if i > 0:
                prev_pts = float(frames[i-1].get('pkt_pts_time', 0))
                curr_pts = float(frame.get('pkt_pts_time', 0))
                gap = curr_pts - prev_pts
                pts_gaps.append(gap)
        
        print(f"📈 帧类型分布: {frame_types}")
        
        # 检查PTS异常
        if pts_gaps:
            avg_gap = sum(pts_gaps) / len(pts_gaps)
            unusual_gaps = [gap for gap in pts_gaps if abs(gap - avg_gap) > avg_gap * 0.5]
            if unusual_gaps:
                print(f"⚠️  发现异常PTS间隔: {len(unusual_gaps)} 个")
                print(f"   平均间隔: {avg_gap:.6f}s")
                print(f"   异常间隔: {unusual_gaps[:5]}")  # 显示前5个
        
        # 检查异常大的帧
        if large_frames:
            print(f"🔍 发现异常大帧: {len(large_frames)} 个")
            for frame in large_frames[:5]:  # 显示前5个
                print(f"   帧{frame['frame_num']}: {frame['size']} bytes, {frame['type']}, PTS={frame['pts_time']}")
        
        return {
            'total_frames': len(frames),
            'frame_types': frame_types,
            'large_frames': large_frames,
            'unusual_pts_gaps': len(unusual_gaps) if 'unusual_gaps' in locals() else 0,
            'frames': frames
        }
        
    except Exception as e:
        print(f"❌ 帧结构分析失败: {e}")
        return None

def check_sei_messages(video_path, tools):
    """检查SEI消息"""
    print("🔍 检查SEI消息...")
    
    ffprobe = tools.get('ffprobe')
    if not ffprobe:
        return None
    
    # 使用ffprobe检查SEI数据
    cmd = [ffprobe, '-v', 'error', '-show_entries', 'packet=data',
           '-select_streams', 'v:0', '-of', 'json', video_path]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ SEI消息检查完成")
            # 这里可以进一步分析SEI数据
            return True
        else:
            print("❌ SEI消息检查失败")
            return False
    except Exception as e:
        print(f"❌ SEI检查失败: {e}")
        return False

def extract_potential_hidden_frames(video_path, tools, output_dir):
    """尝试提取潜在的隐藏帧"""
    print("🔍 尝试提取隐藏帧...")
    
    ffmpeg = tools.get('ffmpeg')
    if not ffmpeg:
        return False
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 方法1: 提取奇数帧（可能是隐藏的B视频）
    odd_frames_dir = os.path.join(output_dir, 'odd_frames')
    os.makedirs(odd_frames_dir, exist_ok=True)
    
    cmd_odd = [ffmpeg, '-i', video_path, '-vf', 'select=mod(n\\,2)',
               '-vsync', 'vfr', f'{odd_frames_dir}/frame_%04d.png']
    
    try:
        result = subprocess.run(cmd_odd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            odd_count = len([f for f in os.listdir(odd_frames_dir) if f.endswith('.png')])
            print(f"✅ 提取奇数帧: {odd_count} 帧")
        else:
            print(f"❌ 奇数帧提取失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 奇数帧提取失败: {e}")
    
    # 方法2: 提取偶数帧（主视频）
    even_frames_dir = os.path.join(output_dir, 'even_frames')
    os.makedirs(even_frames_dir, exist_ok=True)
    
    cmd_even = [ffmpeg, '-i', video_path, '-vf', 'select=not(mod(n\\,2))',
                '-vsync', 'vfr', f'{even_frames_dir}/frame_%04d.png']
    
    try:
        result = subprocess.run(cmd_even, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            even_count = len([f for f in os.listdir(even_frames_dir) if f.endswith('.png')])
            print(f"✅ 提取偶数帧: {even_count} 帧")
        else:
            print(f"❌ 偶数帧提取失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 偶数帧提取失败: {e}")
    
    return True

def analyze_douyin_compatibility(video_path, tools):
    """分析抖音播放器兼容性"""
    print("🔍 分析抖音播放器兼容性...")
    
    ffprobe = tools.get('ffprobe')
    if not ffprobe:
        return None
    
    # 检查编码参数
    cmd = [ffprobe, '-v', 'error', '-show_entries', 'stream=codec_name,profile,level,refs,has_b_frames',
           '-select_streams', 'v:0', '-of', 'json', video_path]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            data = json.loads(result.stdout)
            stream = data['streams'][0]
            
            print(f"📱 编码兼容性分析:")
            print(f"   编码器: {stream.get('codec_name')}")
            print(f"   Profile: {stream.get('profile')}")
            print(f"   Level: {stream.get('level')}")
            print(f"   参考帧: {stream.get('refs')}")
            print(f"   B帧数: {stream.get('has_b_frames')}")
            
            # 抖音可能对B帧处理有特殊逻辑
            has_b_frames = int(stream.get('has_b_frames', 0))
            if has_b_frames > 0:
                print(f"⚠️  检测到B帧结构，这可能是隐写技术的关键")
                return True
            
        return False
    except Exception as e:
        print(f"❌ 兼容性分析失败: {e}")
        return False

def detect_steganography_type(video_path):
    """主检测函数"""
    print("=" * 80)
    print("🕵️  高级视频隐写技术检测器")
    print("=" * 80)
    print(f"📁 目标文件: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 文件不存在: {video_path}")
        return
    
    # 检查工具
    tools = check_tools()
    if not tools.get('ffmpeg') or not tools.get('ffprobe'):
        print("❌ 缺少必要工具 (ffmpeg/ffprobe)")
        return
    
    print(f"✅ 工具检查完成")
    
    # 创建临时目录
    temp_dir = f"steganography_analysis_{int(time.time())}"
    
    try:
        # 1. 基本信息分析
        print("\n" + "="*50)
        print("📊 基本信息分析")
        print("="*50)
        
        # 2. 帧结构分析
        print("\n" + "="*50)
        print("🔍 帧结构分析")
        print("="*50)
        frame_analysis = analyze_frame_structure(video_path, tools)
        
        # 3. SEI消息检查
        print("\n" + "="*50)
        print("🔍 SEI消息检查")
        print("="*50)
        sei_result = check_sei_messages(video_path, tools)
        
        # 4. 抖音兼容性分析
        print("\n" + "="*50)
        print("📱 抖音播放器兼容性分析")
        print("="*50)
        douyin_compat = analyze_douyin_compatibility(video_path, tools)
        
        # 5. 尝试提取隐藏内容
        print("\n" + "="*50)
        print("🎯 隐藏内容提取")
        print("="*50)
        extract_potential_hidden_frames(video_path, tools, temp_dir)
        
        # 6. 综合分析结论
        print("\n" + "="*80)
        print("📋 综合分析结论")
        print("="*80)
        
        steganography_detected = False
        techniques = []
        
        if frame_analysis:
            if frame_analysis.get('unusual_pts_gaps', 0) > 0:
                techniques.append("PTS时间戳操控")
                steganography_detected = True
            
            if frame_analysis.get('large_frames'):
                techniques.append("异常大帧嵌入")
                steganography_detected = True
        
        if douyin_compat:
            techniques.append("B帧结构隐写")
            steganography_detected = True
        
        if steganography_detected:
            print("🚨 检测结果: 发现视频隐写技术!")
            print(f"🔍 检测到的技术: {', '.join(techniques)}")
            print(f"📱 抖音预览可见原理: 抖音播放器在生成缩略图时可能解析了B帧或特殊帧")
            print(f"🎯 建议提取方法: 使用奇偶帧分离技术")
        else:
            print("✅ 未检测到明显的隐写技术")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"🧹 清理临时文件: {temp_dir}")
            except:
                pass

if __name__ == "__main__":
    # 分析目标文件
    target_video = "../shp/966.mp4"
    detect_steganography_type(target_video)
