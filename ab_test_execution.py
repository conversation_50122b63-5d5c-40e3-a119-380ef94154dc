#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧技术完整合成测试执行脚本
"""

import os
import time
import json
from ab_frame_interleaving_toolkit import ABFrameInterleavingToolkit

def main():
    print("🧪 AB融帧技术完整合成测试")
    print("=" * 80)
    
    # 输入文件路径
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    # 输出文件路径
    ab_output_path = "ab_interleaved_test_output.mp4"
    extracted_a_path = "extracted_a_test.mp4"
    extracted_b_path = "extracted_b_test.mp4"
    
    # 初始化工具包
    print("🔧 初始化AB融帧工具包...")
    toolkit = ABFrameInterleavingToolkit()
    
    # 测试结果记录
    test_results = {
        "start_time": time.time(),
        "input_files": {
            "video_a": video_a_path,
            "video_b": video_b_path
        },
        "tests": {}
    }
    
    try:
        # 步骤1: 检查输入文件
        print("\n📋 步骤1: 检查输入文件")
        print("-" * 50)
        
        if not os.path.exists(video_a_path):
            print(f"❌ 视频A不存在: {video_a_path}")
            return False
        
        if not os.path.exists(video_b_path):
            print(f"❌ 视频B不存在: {video_b_path}")
            return False
        
        print("✅ 输入文件检查通过")
        
        # 获取视频信息
        print("\n📊 获取视频信息...")
        info_a = toolkit.get_video_info(video_a_path)
        info_b = toolkit.get_video_info(video_b_path)
        
        if info_a:
            print(f"视频A: {info_a['width']}x{info_a['height']}, {info_a['fps']:.2f}fps, {info_a['duration']:.2f}s, {info_a['nb_frames']}帧")
        if info_b:
            print(f"视频B: {info_b['width']}x{info_b['height']}, {info_b['fps']:.2f}fps, {info_b['duration']:.2f}s, {info_b['nb_frames']}帧")
        
        test_results["tests"]["input_check"] = {
            "status": "success",
            "video_a_info": info_a,
            "video_b_info": info_b
        }
        
        # 步骤2: 创建AB融帧视频
        print("\n🎬 步骤2: 创建AB融帧视频")
        print("-" * 50)
        
        start_time = time.time()
        success = toolkit.create_ab_interleaved_video(
            video_a_path=video_a_path,
            video_b_path=video_b_path,
            output_path=ab_output_path,
            resolution='vertical_hd'  # 使用竖屏HD分辨率
        )
        creation_time = time.time() - start_time
        
        if success and os.path.exists(ab_output_path):
            file_size = os.path.getsize(ab_output_path) / 1024 / 1024
            print(f"✅ AB融帧视频创建成功!")
            print(f"📁 输出文件: {ab_output_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            print(f"⏱️ 创建耗时: {creation_time:.2f} 秒")
            
            # 获取输出视频信息
            ab_info = toolkit.get_video_info(ab_output_path)
            if ab_info:
                print(f"📺 AB视频信息: {ab_info['width']}x{ab_info['height']}, {ab_info['fps']:.2f}fps, {ab_info['duration']:.2f}s, {ab_info['nb_frames']}帧")
            
            test_results["tests"]["ab_creation"] = {
                "status": "success",
                "output_file": ab_output_path,
                "file_size_mb": file_size,
                "creation_time": creation_time,
                "ab_video_info": ab_info
            }
        else:
            print("❌ AB融帧视频创建失败")
            test_results["tests"]["ab_creation"] = {
                "status": "failed",
                "error": "Creation failed"
            }
            return False
        
        # 步骤3: 检测AB融帧技术
        print("\n🔍 步骤3: 检测AB融帧技术")
        print("-" * 50)
        
        detected = toolkit.detect_ab_interleaving(ab_output_path)
        if detected:
            print("✅ AB融帧技术检测成功!")
        else:
            print("❌ 未检测到AB融帧技术")
        
        test_results["tests"]["ab_detection"] = {
            "status": "success" if detected else "failed",
            "detected": detected
        }
        
        # 步骤4: 提取隐藏视频
        print("\n🎯 步骤4: 提取隐藏视频")
        print("-" * 50)
        
        # 提取A视频（主视频）
        print("提取A视频（主视频）...")
        success_a = toolkit.extract_hidden_video(ab_output_path, extracted_a_path, 'A')
        
        # 提取B视频（隐藏视频）
        print("提取B视频（隐藏视频）...")
        success_b = toolkit.extract_hidden_video(ab_output_path, extracted_b_path, 'B')
        
        if success_a and success_b:
            print("✅ 视频提取成功!")
            
            # 获取提取视频的信息
            extracted_a_info = toolkit.get_video_info(extracted_a_path)
            extracted_b_info = toolkit.get_video_info(extracted_b_path)
            
            if extracted_a_info:
                print(f"📺 提取的A视频: {extracted_a_info['nb_frames']}帧, {extracted_a_info['duration']:.2f}s")
            if extracted_b_info:
                print(f"📺 提取的B视频: {extracted_b_info['nb_frames']}帧, {extracted_b_info['duration']:.2f}s")
            
            test_results["tests"]["extraction"] = {
                "status": "success",
                "extracted_a_info": extracted_a_info,
                "extracted_b_info": extracted_b_info
            }
        else:
            print("❌ 视频提取失败")
            test_results["tests"]["extraction"] = {
                "status": "failed",
                "success_a": success_a,
                "success_b": success_b
            }
        
        # 步骤5: 播放器兼容性验证
        print("\n📱 步骤5: 播放器兼容性验证")
        print("-" * 50)
        
        compatibility = toolkit.verify_compatibility(ab_output_path)
        test_results["tests"]["compatibility"] = {
            "status": "success" if compatibility else "failed",
            "compatible": compatibility
        }
        
        # 生成测试报告
        test_results["end_time"] = time.time()
        test_results["total_duration"] = test_results["end_time"] - test_results["start_time"]
        
        # 保存测试报告
        report_file = f"ab_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 测试报告已保存: {report_file}")
        
        # 总结
        print("\n🎉 测试完成总结")
        print("=" * 80)
        
        total_tests = len(test_results["tests"])
        passed_tests = sum(1 for test in test_results["tests"].values() if test["status"] == "success")
        
        print(f"总测试项: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"总耗时: {test_results['total_duration']:.2f} 秒")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！AB融帧技术工作正常！")
            return True
        else:
            print("\n⚠️ 部分测试失败，请检查详细报告")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
        test_results["error"] = str(e)
        return False

if __name__ == "__main__":
    main()
