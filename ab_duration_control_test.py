#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧时长控制方案测试
测试零时长和负时长B帧方案的可行性
"""

import os
import subprocess
import tempfile
import shutil
import time

class ABDurationControlTester:
    """AB融帧时长控制测试器"""
    
    def __init__(self):
        self.ffmpeg_cmd = 'ffmpeg'
        
    def create_duration_controlled_frame_list(self, frames_a_dir, frames_b_dir, output_file, b_duration_mode):
        """创建时长控制的帧列表"""
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])
        
        count_a = len(frames_a)
        count_b = len(frames_b)
        
        print(f"🎛️ 创建时长控制帧列表")
        print(f"  A帧数: {count_a}")
        print(f"  B帧数: {count_b}")
        print(f"  B帧时长模式: {b_duration_mode}")
        
        # 设置B帧时长
        if b_duration_mode == "zero":
            b_duration = "0.000000"
            print(f"  B帧时长: 0秒 (零时长)")
        elif b_duration_mode == "micro":
            b_duration = "0.000001"
            print(f"  B帧时长: 0.000001秒 (微秒级)")
        elif b_duration_mode == "negative":
            b_duration = "-0.033333"
            print(f"  B帧时长: -0.033333秒 (负时长)")
        elif b_duration_mode == "nano":
            b_duration = "0.000000001"
            print(f"  B帧时长: 0.000000001秒 (纳秒级)")
        else:
            b_duration = "0.033333"
            print(f"  B帧时长: 0.033333秒 (标准)")
        
        # A帧标准时长
        a_duration = "0.033333"  # 30fps标准
        
        total_frames = 0
        
        with open(output_file, 'w') as f:
            for i in range(count_a):
                # A帧 (标准时长)
                a_frame_path = os.path.join(frames_a_dir, frames_a[i]).replace('\\', '/')
                f.write(f"file '{a_frame_path}'\n")
                f.write(f"duration {a_duration}\n")
                total_frames += 1
                
                # B帧 (控制时长)
                b_index = i % count_b
                b_frame_path = os.path.join(frames_b_dir, frames_b[b_index]).replace('\\', '/')
                f.write(f"file '{b_frame_path}'\n")
                f.write(f"duration {b_duration}\n")
                total_frames += 1
        
        print(f"✓ 时长控制列表创建完成，总帧数: {total_frames}")
        return total_frames
    
    def test_duration_control_method(self, video_a_path, video_b_path, b_duration_mode, output_path):
        """测试特定的时长控制方法"""
        print(f"\n🧪 测试时长控制方法: {b_duration_mode.upper()}")
        print("=" * 80)
        
        temp_dir = tempfile.mkdtemp(prefix=f'ab_duration_{b_duration_mode}_')
        
        try:
            # 步骤1: 提取A视频帧
            print("\n📋 步骤1: 提取A视频帧")
            print("-" * 50)
            
            frames_a_dir = os.path.join(temp_dir, 'frames_a')
            os.makedirs(frames_a_dir, exist_ok=True)
            
            cmd_a = [
                self.ffmpeg_cmd, '-y', '-i', video_a_path,
                '-vsync', '0',
                f'{frames_a_dir}/frame_%06d.png'
            ]
            
            result_a = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
            if result_a.returncode != 0:
                print(f"❌ A视频帧提取失败: {result_a.stderr}")
                return False
            
            frames_a = [f for f in os.listdir(frames_a_dir) if f.endswith('.png')]
            count_a = len(frames_a)
            print(f"✓ A视频帧提取完成: {count_a}帧")
            
            # 步骤2: 提取B视频帧
            print("\n📋 步骤2: 提取B视频帧")
            print("-" * 50)
            
            frames_b_dir = os.path.join(temp_dir, 'frames_b')
            os.makedirs(frames_b_dir, exist_ok=True)
            
            cmd_b = [
                self.ffmpeg_cmd, '-y', '-i', video_b_path,
                '-vsync', '0',
                f'{frames_b_dir}/frame_%06d.png'
            ]
            
            result_b = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
            if result_b.returncode != 0:
                print(f"❌ B视频帧提取失败: {result_b.stderr}")
                return False
            
            frames_b = [f for f in os.listdir(frames_b_dir) if f.endswith('.png')]
            count_b = len(frames_b)
            print(f"✓ B视频帧提取完成: {count_b}帧")
            
            # 步骤3: 创建时长控制帧列表
            print("\n📋 步骤3: 创建时长控制帧列表")
            print("-" * 50)
            
            frame_list = os.path.join(temp_dir, 'frame_list.txt')
            total_frames = self.create_duration_controlled_frame_list(
                frames_a_dir, frames_b_dir, frame_list, b_duration_mode
            )
            
            # 步骤4: 提取音频
            print("\n📋 步骤4: 提取音频")
            print("-" * 50)
            
            audio_file = os.path.join(temp_dir, 'audio.aac')
            audio_cmd = [
                self.ffmpeg_cmd, '-y', '-i', video_a_path,
                '-vn', '-acodec', 'copy', audio_file
            ]
            
            audio_result = subprocess.run(audio_cmd, capture_output=True, text=True, timeout=60)
            has_audio = audio_result.returncode == 0
            
            if has_audio:
                print("✓ 音频提取成功")
            else:
                print("⚠️ 音频提取失败，将创建无音频视频")
            
            # 步骤5: 使用concat demuxer创建视频
            print("\n📋 步骤5: 时长控制编码")
            print("-" * 50)
            
            # 先创建无音频版本
            temp_video = os.path.join(temp_dir, 'temp_video.mp4')
            
            concat_cmd = [
                self.ffmpeg_cmd, '-y',
                '-f', 'concat',
                '-safe', '0',
                '-i', frame_list,
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '20',
                '-pix_fmt', 'yuv420p',
                '-r', '30',
                '-vsync', 'cfr',
                '-avoid_negative_ts', 'make_zero',
                temp_video
            ]
            
            print(f"开始{b_duration_mode}时长编码...")
            start_time = time.time()
            
            result = subprocess.run(concat_cmd, capture_output=True, text=True, timeout=600)
            
            encode_time = time.time() - start_time
            
            if result.returncode != 0:
                print(f"❌ 编码失败: {result.stderr}")
                return False
            
            print(f"✓ 编码完成，耗时: {encode_time:.2f}秒")
            
            # 步骤6: 添加音频（如果有）
            if has_audio:
                print("\n📋 步骤6: 添加音频")
                print("-" * 50)
                
                final_cmd = [
                    self.ffmpeg_cmd, '-y',
                    '-i', temp_video,
                    '-i', audio_file,
                    '-c:v', 'copy',
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    '-ar', '44100',
                    '-ac', '2',
                    '-shortest',
                    '-metadata', f'comment=AB_FRAME_INTERLEAVING_DURATION_CONTROL_{b_duration_mode.upper()}',
                    '-metadata', f'b_frame_duration={b_duration_mode}',
                    '-metadata', f'total_frames={total_frames}',
                    '-metadata', f'a_frames={count_a}',
                    output_path
                ]
                
                final_result = subprocess.run(final_cmd, capture_output=True, text=True, timeout=300)
                
                if final_result.returncode != 0:
                    print(f"❌ 音频合并失败: {final_result.stderr}")
                    shutil.copy2(temp_video, output_path)
                else:
                    print("✓ 音频合并完成")
            else:
                shutil.copy2(temp_video, output_path)
            
            # 显示结果
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024
                
                print(f"\n✅ {b_duration_mode.upper()}时长控制视频创建成功!")
                print(f"📁 输出文件: {os.path.basename(output_path)}")
                print(f"📊 文件大小: {file_size:.2f} MB")
                print(f"🎞️ 理论帧数: {total_frames}")
                print(f"🎯 A帧数: {count_a}")
                print(f"🎛️ B帧时长控制: {b_duration_mode}")
                
                return True
            else:
                print("❌ 输出文件未生成")
                return False
                
        except Exception as e:
            print(f"处理出错: {e}")
            return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"🧹 清理临时文件: {temp_dir}")
                except:
                    pass
    
    def verify_duration_control_effectiveness(self, test_video_path, original_a_path, b_duration_mode):
        """验证时长控制效果"""
        print(f"\n🔍 {b_duration_mode.upper()}时长控制效果验证")
        print("=" * 80)
        
        # 1. 基本视频信息
        print("📊 基本视频信息:")
        cmd_info = [
            'ffprobe', '-v', 'error', '-show_format', '-show_streams',
            '-select_streams', 'v:0', '-of', 'json', test_video_path
        ]
        
        try:
            result = subprocess.run(cmd_info, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                
                video_stream = None
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break
                
                format_info = data.get('format', {})
                
                if video_stream:
                    print(f"  分辨率: {video_stream['width']}x{video_stream['height']}")
                    print(f"  帧率: {video_stream['r_frame_rate']}")
                    print(f"  总帧数: {video_stream['nb_frames']}")
                    print(f"  时长: {float(format_info['duration']):.3f}秒")
                    print(f"  编码: {video_stream['codec_name']}")
                
        except Exception as e:
            print(f"⚠️ 信息获取失败: {e}")
        
        # 2. A帧完整性验证
        print(f"\n🎯 A帧完整性验证:")
        
        # 获取原始A帧数
        cmd_orig = [
            'ffprobe', '-v', 'error', '-select_streams', 'v:0',
            '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0',
            original_a_path
        ]
        
        result_orig = subprocess.run(cmd_orig, capture_output=True, text=True, timeout=30)
        if result_orig.returncode == 0:
            original_frames = int(result_orig.stdout.strip())
            
            # 提取A帧验证
            temp_dir = tempfile.mkdtemp(prefix=f'verify_{b_duration_mode}_')
            try:
                extract_cmd = [
                    self.ffmpeg_cmd, '-y', '-i', test_video_path,
                    '-vf', 'select=not(mod(n\\,2))',
                    '-vsync', '0',
                    f'{temp_dir}/a_frame_%06d.png'
                ]
                
                result = subprocess.run(extract_cmd, capture_output=True, text=True, timeout=300)
                if result.returncode == 0:
                    extracted_files = [f for f in os.listdir(temp_dir) if f.endswith('.png')]
                    extracted_frames = len(extracted_files)
                    
                    print(f"  原始A帧数: {original_frames}")
                    print(f"  提取A帧数: {extracted_frames}")
                    print(f"  完整性: {extracted_frames/original_frames*100:.1f}%")
                    
                    if extracted_frames >= original_frames * 0.95:
                        print("  ✅ A帧完整性良好")
                        integrity_ok = True
                    else:
                        print("  ⚠️ A帧可能有缺失")
                        integrity_ok = False
                else:
                    print("  ❌ A帧提取失败")
                    integrity_ok = False
                    
            finally:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
        else:
            integrity_ok = False
        
        # 3. B帧可见性测试
        print(f"\n👁️ B帧可见性测试:")
        print(f"  时长控制方法: {b_duration_mode}")
        
        if b_duration_mode == "zero":
            print("  预期效果: B帧零时长，播放器应跳过")
        elif b_duration_mode == "micro":
            print("  预期效果: B帧微秒显示，肉眼不可察觉")
        elif b_duration_mode == "negative":
            print("  预期效果: 负时长可能导致帧回退或跳过")
        elif b_duration_mode == "nano":
            print("  预期效果: 纳秒级显示，理论上不可见")
        
        print("  🎬 手动验证建议:")
        print(f"    1. 在VLC中播放 {os.path.basename(test_video_path)}")
        print("    2. 观察是否有B帧闪烁现象")
        print("    3. 对比原始A视频播放效果")
        print("    4. 测试在移动端播放器的表现")
        
        return integrity_ok
    
    def run_comprehensive_duration_tests(self, video_a_path, video_b_path):
        """运行全面的时长控制测试"""
        print("🧪 AB融帧时长控制方案全面测试")
        print("=" * 80)
        
        # 测试方案列表
        test_methods = [
            ("zero", "ab_duration_zero.mp4"),
            ("micro", "ab_duration_micro.mp4"),
            ("negative", "ab_duration_negative.mp4"),
            ("nano", "ab_duration_nano.mp4")
        ]
        
        results = {}
        
        for method, output_file in test_methods:
            print(f"\n{'='*20} 测试方案: {method.upper()} {'='*20}")
            
            # 创建测试视频
            success = self.test_duration_control_method(
                video_a_path, video_b_path, method, output_file
            )
            
            if success:
                # 验证效果
                integrity_ok = self.verify_duration_control_effectiveness(
                    output_file, video_a_path, method
                )
                
                results[method] = {
                    'success': True,
                    'file': output_file,
                    'integrity': integrity_ok
                }
            else:
                results[method] = {
                    'success': False,
                    'file': None,
                    'integrity': False
                }
        
        # 生成测试报告
        print(f"\n📊 时长控制方案测试报告")
        print("=" * 80)
        
        for method, result in results.items():
            status = "✅ 成功" if result['success'] else "❌ 失败"
            integrity = "✅ 良好" if result['integrity'] else "⚠️ 需优化"
            
            print(f"{method.upper():12} | 创建: {status:8} | A帧完整性: {integrity:10} | 文件: {result['file'] or 'N/A'}")
        
        # 推荐最佳方案
        print(f"\n🏆 推荐方案:")
        successful_methods = [m for m, r in results.items() if r['success'] and r['integrity']]
        
        if successful_methods:
            best_method = successful_methods[0]  # 可以根据需要调整选择逻辑
            print(f"  最佳方案: {best_method.upper()}")
            print(f"  推荐文件: {results[best_method]['file']}")
            print(f"  特点: 时长控制 + A帧完整性保证")
        else:
            print("  ⚠️ 所有方案都需要进一步优化")
        
        return results


def main():
    """主程序"""
    print("🧪 AB融帧时长控制方案测试")
    print("=" * 80)
    
    # 输入文件
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\e.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    
    tester = ABDurationControlTester()
    
    # 运行全面测试
    results = tester.run_comprehensive_duration_tests(video_a_path, video_b_path)
    
    print(f"\n🎉 时长控制方案测试完成!")
    print(f"请手动验证生成的视频文件，确认B帧可见性控制效果")


if __name__ == "__main__":
    main()
