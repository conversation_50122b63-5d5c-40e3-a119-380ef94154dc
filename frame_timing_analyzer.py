#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帧时间戳分析器
详细分析AB融帧视频中每一帧的实际时长
"""

import subprocess
import json
import csv
import os

class FrameTimingAnalyzer:
    """帧时间戳分析器"""
    
    def __init__(self):
        self.ffprobe_cmd = 'ffprobe'
        
    def analyze_frame_timestamps(self, video_path, max_frames=50):
        """分析帧时间戳"""
        print(f"🔍 分析视频帧时间戳: {os.path.basename(video_path)}")
        print("=" * 80)
        
        # 获取详细的帧信息
        cmd = [
            self.ffprobe_cmd, '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'frame=n,pkt_pts_time,pkt_duration_time,pict_type',
            '-of', 'csv=p=0',
            '-read_intervals', f'%+#{max_frames}',  # 只分析前N帧
            video_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode != 0:
                print(f"❌ FFprobe执行失败: {result.stderr}")
                return None
            
            # 解析帧数据
            lines = result.stdout.strip().split('\n')
            frames = []
            
            for line in lines:
                if line and ',' in line:
                    parts = line.split(',')
                    if len(parts) >= 4:
                        try:
                            frame_num = int(parts[0])
                            pts_time = float(parts[1]) if parts[1] else 0
                            duration = float(parts[2]) if parts[2] else 0
                            frame_type = parts[3] if parts[3] else 'Unknown'
                            
                            frames.append({
                                'frame_num': frame_num,
                                'pts_time': pts_time,
                                'duration': duration,
                                'frame_type': frame_type
                            })
                        except ValueError:
                            continue
            
            if not frames:
                print("❌ 未能解析到帧数据")
                return None
            
            print(f"✓ 成功解析 {len(frames)} 帧数据")
            return frames
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def analyze_frame_intervals(self, frames):
        """分析帧间隔"""
        print(f"\n📊 帧间隔分析")
        print("-" * 50)
        
        intervals = []
        for i in range(1, len(frames)):
            interval = frames[i]['pts_time'] - frames[i-1]['pts_time']
            intervals.append(interval)
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            min_interval = min(intervals)
            max_interval = max(intervals)
            
            print(f"平均帧间隔: {avg_interval:.6f}秒")
            print(f"最小帧间隔: {min_interval:.6f}秒")
            print(f"最大帧间隔: {max_interval:.6f}秒")
            print(f"标准30fps间隔: 0.033333秒")
            
            # 检查是否有异常短的间隔（可能是B帧）
            short_intervals = [i for i in intervals if i < 0.01]  # 小于10ms
            if short_intervals:
                print(f"检测到 {len(short_intervals)} 个短间隔:")
                for i, interval in enumerate(short_intervals[:10]):  # 只显示前10个
                    print(f"  短间隔 {i+1}: {interval:.9f}秒")
            else:
                print("未检测到明显的短间隔")
        
        return intervals
    
    def detailed_frame_analysis(self, frames, max_display=20):
        """详细帧分析"""
        print(f"\n📋 详细帧分析 (显示前{max_display}帧)")
        print("-" * 80)
        print(f"{'帧号':>4} | {'时间戳(秒)':>12} | {'时长(秒)':>12} | {'间隔(秒)':>12} | {'类型':>6} | {'推测'}")
        print("-" * 80)
        
        for i, frame in enumerate(frames[:max_display]):
            # 计算与前一帧的间隔
            if i > 0:
                interval = frame['pts_time'] - frames[i-1]['pts_time']
            else:
                interval = 0
            
            # 推测帧类型（基于间隔）
            if i == 0:
                guess = "首帧"
            elif interval < 0.01:  # 小于10ms
                guess = "可能B帧"
            elif 0.03 <= interval <= 0.04:  # 接近30fps
                guess = "可能A帧"
            else:
                guess = "异常"
            
            print(f"{frame['frame_num']:>4} | {frame['pts_time']:>12.6f} | {frame['duration']:>12.6f} | {interval:>12.6f} | {frame['frame_type']:>6} | {guess}")
    
    def verify_b_frame_duration(self, frames):
        """验证B帧时长设置"""
        print(f"\n🎯 B帧时长验证")
        print("-" * 50)
        
        # 分析奇偶帧的时长分布
        even_frames = [f for f in frames if f['frame_num'] % 2 == 0]  # 偶数帧（A帧）
        odd_frames = [f for f in frames if f['frame_num'] % 2 == 1]   # 奇数帧（B帧）
        
        if even_frames:
            even_durations = [f['duration'] for f in even_frames if f['duration'] > 0]
            if even_durations:
                avg_even_duration = sum(even_durations) / len(even_durations)
                print(f"A帧(偶数帧)平均时长: {avg_even_duration:.6f}秒")
            else:
                print("A帧时长数据不可用")
        
        if odd_frames:
            odd_durations = [f['duration'] for f in odd_frames if f['duration'] > 0]
            if odd_durations:
                avg_odd_duration = sum(odd_durations) / len(odd_durations)
                print(f"B帧(奇数帧)平均时长: {avg_odd_duration:.6f}秒")
                
                # 检查是否真的是微秒级
                if avg_odd_duration < 0.000010:  # 小于10微秒
                    print("✅ B帧确实设置为微秒级时长")
                elif avg_odd_duration < 0.001:  # 小于1毫秒
                    print("⚠️ B帧为毫秒级时长，可能仍可见")
                else:
                    print("❌ B帧时长设置无效，仍为正常时长")
            else:
                print("B帧时长数据不可用")
        
        # 检查间隔分布
        intervals = []
        for i in range(1, len(frames)):
            interval = frames[i]['pts_time'] - frames[i-1]['pts_time']
            intervals.append(interval)
        
        if intervals:
            # 统计不同间隔范围的数量
            micro_intervals = [i for i in intervals if i < 0.000010]  # 微秒级
            milli_intervals = [i for i in intervals if 0.000010 <= i < 0.001]  # 毫秒级
            normal_intervals = [i for i in intervals if i >= 0.030]  # 正常级
            
            print(f"\n间隔分布统计:")
            print(f"  微秒级间隔 (<10μs): {len(micro_intervals)}个")
            print(f"  毫秒级间隔 (10μs-1ms): {len(milli_intervals)}个")
            print(f"  正常级间隔 (≥30ms): {len(normal_intervals)}个")
            
            if micro_intervals:
                print("✅ 检测到微秒级间隔，B帧时长控制可能有效")
            elif milli_intervals:
                print("⚠️ 只检测到毫秒级间隔，B帧可能仍可见")
            else:
                print("❌ 未检测到短间隔，时长控制可能无效")
    
    def comprehensive_analysis(self, video_path):
        """综合分析"""
        print(f"🔬 AB融帧视频综合时间戳分析")
        print(f"文件: {os.path.basename(video_path)}")
        print("=" * 80)
        
        # 分析帧时间戳
        frames = self.analyze_frame_timestamps(video_path, max_frames=100)
        if not frames:
            return False
        
        # 分析帧间隔
        intervals = self.analyze_frame_intervals(frames)
        
        # 详细帧分析
        self.detailed_frame_analysis(frames)
        
        # 验证B帧时长
        self.verify_b_frame_duration(frames)
        
        # 生成结论
        print(f"\n🎯 分析结论")
        print("-" * 50)
        
        # 检查是否真的有微秒级控制
        short_intervals = [i for i in intervals if i < 0.01]
        if short_intervals:
            min_interval = min(short_intervals)
            print(f"✓ 检测到最短间隔: {min_interval:.9f}秒")
            
            if min_interval < 0.000010:
                print("✅ B帧时长控制有效，理论上应该不可见")
                effectiveness = "有效"
            elif min_interval < 0.001:
                print("⚠️ B帧时长控制部分有效，但可能仍有轻微闪烁")
                effectiveness = "部分有效"
            else:
                print("❌ B帧时长控制无效，闪烁问题未解决")
                effectiveness = "无效"
        else:
            print("❌ 未检测到时长控制效果")
            effectiveness = "无效"
        
        return effectiveness


def main():
    """主程序"""
    print("🔬 AB融帧视频帧时间戳分析器")
    print("=" * 80)
    
    # 分析目标文件
    video_files = [
        "ab_optimal_invisible.mp4",
        "ab_duration_micro.mp4",
        "ab_duration_zero.mp4"
    ]
    
    analyzer = FrameTimingAnalyzer()
    
    for video_file in video_files:
        if os.path.exists(video_file):
            print(f"\n{'='*20} 分析文件: {video_file} {'='*20}")
            effectiveness = analyzer.comprehensive_analysis(video_file)
            print(f"时长控制效果: {effectiveness}")
        else:
            print(f"⚠️ 文件不存在: {video_file}")
    
    print(f"\n🎉 帧时间戳分析完成!")


if __name__ == "__main__":
    main()
