#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AB融帧视频隐写技术完整工具包
基于966.mp4技术分析的精确复刻实现

功能特性:
1. 精确的1:1帧交替算法
2. 基于966.mp4参数的优化编码
3. 完整的生成、检测、提取功能
4. 播放器兼容性验证
"""

import os
import subprocess
import tempfile
import shutil
import json
import time
from pathlib import Path

class ABFrameInterleavingToolkit:
    """AB融帧技术工具包"""
    
    def __init__(self):
        self.ffmpeg_cmd = self._check_ffmpeg()
        self.ffprobe_cmd = self._check_ffprobe()
        
        # 基于966.mp4分析的优化参数
        self.encoding_params = {
            'codec': 'libx264',
            'preset': 'medium',
            'crf': '18',
            'fps': '30',
            'gop_size': '60',
            'keyint_min': '60',
            'sc_threshold': '0',
            'pix_fmt': 'yuv420p',
            'profile': 'high',
            'level': '5.0'
        }
        
        # 标准分辨率配置
        self.resolution_configs = {
            'vertical_hd': {'width': 1080, 'height': 1920},  # 966.mp4使用的分辨率
            'horizontal_hd': {'width': 1920, 'height': 1080},
            'square': {'width': 1080, 'height': 1080}
        }
    
    def _check_ffmpeg(self):
        """检查FFmpeg可用性"""
        for cmd in ['ffmpeg', 'ffmpeg.exe']:
            try:
                result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    return cmd
            except:
                continue
        raise RuntimeError("FFmpeg未找到，请确保已正确安装")
    
    def _check_ffprobe(self):
        """检查FFprobe可用性"""
        for cmd in ['ffprobe', 'ffprobe.exe']:
            try:
                result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
                if result.returncode == 0:
                    return cmd
            except:
                continue
        raise RuntimeError("FFprobe未找到，请确保已正确安装")
    
    def get_video_info(self, video_path):
        """获取视频详细信息"""
        cmd = [
            self.ffprobe_cmd, '-v', 'error', '-show_format', '-show_streams',
            '-of', 'json', video_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                
                video_stream = None
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video':
                        video_stream = stream
                        break
                
                if video_stream:
                    return {
                        'width': int(video_stream.get('width', 0)),
                        'height': int(video_stream.get('height', 0)),
                        'fps': eval(video_stream.get('r_frame_rate', '30/1')),
                        'duration': float(data.get('format', {}).get('duration', 0)),
                        'nb_frames': int(video_stream.get('nb_frames', 0)),
                        'codec_name': video_stream.get('codec_name'),
                        'file_size': int(data.get('format', {}).get('size', 0))
                    }
        except Exception as e:
            print(f"获取视频信息失败: {e}")
        
        return None
    
    def preprocess_video(self, input_path, output_path, target_resolution='vertical_hd'):
        """预处理视频，统一参数"""
        print(f"预处理视频: {os.path.basename(input_path)}")
        
        resolution = self.resolution_configs[target_resolution]
        
        cmd = [
            self.ffmpeg_cmd, '-y', '-i', input_path,
            '-vf', f'scale={resolution["width"]}:{resolution["height"]},fps={self.encoding_params["fps"]},format={self.encoding_params["pix_fmt"]}',
            '-c:v', self.encoding_params['codec'],
            '-preset', self.encoding_params['preset'],
            '-crf', self.encoding_params['crf'],
            '-pix_fmt', self.encoding_params['pix_fmt'],
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print(f"✓ 预处理完成: {os.path.basename(output_path)}")
                return True
            else:
                print(f"✗ 预处理失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"预处理出错: {e}")
            return False
    
    def extract_frames(self, video_path, output_dir):
        """提取视频帧"""
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [
            self.ffmpeg_cmd, '-y', '-i', video_path,
            os.path.join(output_dir, 'frame_%06d.png')
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                frame_count = len([f for f in os.listdir(output_dir) if f.endswith('.png')])
                print(f"✓ 提取帧数: {frame_count}")
                return frame_count
            else:
                print(f"✗ 帧提取失败: {result.stderr}")
                return 0
        except Exception as e:
            print(f"帧提取出错: {e}")
            return 0
    
    def create_interleaved_frame_list(self, frames_a_dir, frames_b_dir, output_file):
        """创建标准1:1帧交替列表（确保A帧完整性）"""
        frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
        frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])

        # 使用A视频的帧数作为基准，确保A帧完整性
        max_frames = len(frames_a)
        total_frames = 0

        print(f"创建标准1:1交替帧列表: A帧{len(frames_a)}, B帧{len(frames_b)}")
        print(f"以A视频为基准，确保A帧完整性: 使用{max_frames}帧")

        with open(output_file, 'w') as f:
            for i in range(max_frames):
                # A帧 (主视频 - 必须完整包含)
                a_frame_path = os.path.join(frames_a_dir, frames_a[i]).replace('\\', '/')
                f.write(f"file '{a_frame_path}'\n")
                f.write(f"duration 0.033333\n")  # 30fps标准帧时间
                total_frames += 1

                # B帧 (隐藏视频 - 循环使用B帧)
                b_index = i % len(frames_b) if frames_b else 0
                if frames_b:
                    b_frame_path = os.path.join(frames_b_dir, frames_b[b_index]).replace('\\', '/')
                    f.write(f"file '{b_frame_path}'\n")
                    f.write(f"duration 0.033333\n")  # 30fps标准帧时间
                    total_frames += 1
                else:
                    # 如果没有B帧，重复A帧
                    f.write(f"file '{a_frame_path}'\n")
                    f.write(f"duration 0.033333\n")
                    total_frames += 1

        print(f"✓ 标准交替序列创建完成，总帧数: {total_frames}")
        print(f"✓ A帧完整性保证: 所有{len(frames_a)}个A帧都已包含")
        return total_frames
    
    def encode_interleaved_video(self, frame_list_file, output_path, total_frames, audio_source=None):
        """使用966.mp4参数编码交替视频（包含音频处理）"""
        print("开始编码AB融帧视频...")

        cmd = [
            self.ffmpeg_cmd, '-y',
            '-f', 'concat',
            '-safe', '0',
            '-i', frame_list_file,
        ]

        # 如果有音频源，添加音频输入
        if audio_source and os.path.exists(audio_source):
            cmd.extend(['-i', audio_source])
            print(f"✓ 添加音频源: {os.path.basename(audio_source)}")

        cmd.extend([
            # 基于966.mp4的精确编码参数
            '-c:v', self.encoding_params['codec'],
            '-preset', self.encoding_params['preset'],
            '-crf', self.encoding_params['crf'],
            '-r', self.encoding_params['fps'],

            # 优化的GOP结构控制（确保1:1交替效果）
            '-g', self.encoding_params['gop_size'],     # 恢复标准GOP
            '-keyint_min', self.encoding_params['keyint_min'],
            '-sc_threshold', self.encoding_params['sc_threshold'],
            '-bf', '2',                   # 恢复B帧支持
            '-refs', '1',                 # 单参考帧

            # H.264高级参数
            '-profile:v', self.encoding_params['profile'],
            '-level:v', self.encoding_params['level'],
            '-pix_fmt', self.encoding_params['pix_fmt'],

            # 音视频同步优化
            '-vsync', 'cfr',              # 恒定帧率
            '-async', '1',                # 音频同步
            '-copyts',                    # 复制时间戳
        ])

        # 音频处理参数
        if audio_source and os.path.exists(audio_source):
            cmd.extend([
                '-c:a', 'aac',           # 音频编码器
                '-b:a', '128k',          # 音频比特率
                '-ar', '44100',          # 音频采样率
                '-ac', '2',              # 音频声道数
                '-map', '0:v:0',         # 映射视频流
                '-map', '1:a:0',         # 映射音频流
                '-shortest'              # 以最短流为准
            ])
        else:
            print("⚠️ 未找到音频源，生成无音频视频")

        cmd.extend([
            # 添加AB融帧标识元数据
            '-metadata', 'comment=AB_FRAME_INTERLEAVING_V4.0_STANDARD_1TO1',
            '-metadata', 'frame_pattern=A1_B1_A2_B2_STANDARD_INTERLEAVED',
            '-metadata', f'total_ab_frames={total_frames}',
            '-metadata', 'technique=966_mp4_standard_1to1_replication',
            '-metadata', 'audio_source=included' if audio_source else 'none',
            '-metadata', 'framerate=30fps_cfr',
            '-metadata', 'a_frame_integrity=guaranteed',

            output_path
        ])

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode == 0:
                print(f"✓ AB融帧视频编码成功: {os.path.basename(output_path)}")
                return True
            else:
                print(f"✗ 编码失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"编码出错: {e}")
            return False
    
    def create_ab_interleaved_video(self, video_a_path, video_b_path, output_path, resolution='vertical_hd'):
        """创建AB融帧视频（主要接口）"""
        print("=" * 80)
        print("🎬 AB融帧视频隐写技术 - 966.mp4精确复刻")
        print("=" * 80)
        print(f"A视频（主视频）: {os.path.basename(video_a_path)}")
        print(f"B视频（隐藏视频）: {os.path.basename(video_b_path)}")
        print(f"输出文件: {os.path.basename(output_path)}")
        print(f"目标分辨率: {resolution}")
        
        # 检查输入文件
        if not os.path.exists(video_a_path):
            print(f"✗ A视频不存在: {video_a_path}")
            return False
        
        if not os.path.exists(video_b_path):
            print(f"✗ B视频不存在: {video_b_path}")
            return False
        
        temp_dir = tempfile.mkdtemp(prefix='ab_interleaving_')
        
        try:
            # 步骤1: 预处理视频
            print(f"\n📋 步骤1: 预处理视频")
            print("-" * 40)
            
            processed_a = os.path.join(temp_dir, 'processed_a.mp4')
            processed_b = os.path.join(temp_dir, 'processed_b.mp4')
            
            if not self.preprocess_video(video_a_path, processed_a, resolution):
                return False
            
            if not self.preprocess_video(video_b_path, processed_b, resolution):
                return False
            
            # 步骤2: 提取帧
            print(f"\n🎞️ 步骤2: 提取视频帧")
            print("-" * 40)
            
            frames_a_dir = os.path.join(temp_dir, 'frames_a')
            frames_b_dir = os.path.join(temp_dir, 'frames_b')
            
            count_a = self.extract_frames(processed_a, frames_a_dir)
            count_b = self.extract_frames(processed_b, frames_b_dir)
            
            if count_a == 0 or count_b == 0:
                print("✗ 帧提取失败")
                return False
            
            # 步骤3: 创建交替序列
            print(f"\n🔄 步骤3: 创建1:1帧交替序列")
            print("-" * 40)
            
            frame_list_file = os.path.join(temp_dir, 'interleaved_frames.txt')
            total_frames = self.create_interleaved_frame_list(frames_a_dir, frames_b_dir, frame_list_file)
            
            # 步骤4: 编码最终视频（包含音频）
            print(f"\n🎬 步骤4: 编码AB融帧视频（包含音频）")
            print("-" * 40)

            # 选择音频源（优先使用视频A的音频）
            audio_source = video_a_path
            success = self.encode_interleaved_video(frame_list_file, output_path, total_frames, audio_source)
            
            if success:
                # 验证结果
                print(f"\n✅ 处理完成!")
                print("=" * 80)
                
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / 1024 / 1024
                    print(f"📁 输出文件: {os.path.basename(output_path)}")
                    print(f"📊 文件大小: {file_size:.2f} MB")
                    print(f"🎞️ 总帧数: {total_frames}")
                    print(f"🔢 A帧数: {count_a}")
                    print(f"🔢 B帧数: {count_b}")

                    # 获取详细信息
                    info = self.get_video_info(output_path)
                    if info:
                        print(f"📺 分辨率: {info['width']}x{info['height']}")
                        print(f"⏱️ 时长: {info['duration']:.2f}秒")
                        print(f"🎯 帧率: {info['fps']:.2f}fps")

                    # 检查音频
                    self._check_audio_streams(output_path)
                
                return True
            else:
                print("✗ AB融帧视频创建失败")
                return False
                
        except Exception as e:
            print(f"处理出错: {e}")
            return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    print(f"🧹 清理临时文件: {temp_dir}")
                except:
                    pass

    def _check_audio_streams(self, video_path):
        """检查视频的音频流"""
        try:
            cmd = [self.ffprobe_cmd, '-v', 'error', '-select_streams', 'a',
                   '-show_entries', 'stream=codec_name,channels,sample_rate',
                   '-of', 'csv=p=0', video_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0 and result.stdout.strip():
                audio_info = result.stdout.strip().split(',')
                if len(audio_info) >= 3:
                    codec, channels, sample_rate = audio_info[0], audio_info[1], audio_info[2]
                    print(f"🔊 音频信息: {codec}, {channels}声道, {sample_rate}Hz")
                else:
                    print("🔊 音频信息: 已包含音频流")
            else:
                print("🔇 音频信息: 无音频流")
        except Exception as e:
            print(f"⚠️ 音频检查失败: {e}")

    def extract_hidden_video(self, ab_video_path, output_path, extract_type='B'):
        """从AB融帧视频提取隐藏视频"""
        print(f"🔍 提取隐藏视频: {extract_type}视频")
        print(f"输入: {os.path.basename(ab_video_path)}")
        print(f"输出: {os.path.basename(output_path)}")

        # 选择提取模式
        if extract_type.upper() == 'B':
            # 提取奇数帧（隐藏的B视频）
            filter_expr = 'select=mod(n\\,2)'
            print("📋 提取模式: 奇数帧（B视频 - 隐藏内容）")
        else:
            # 提取偶数帧（主A视频）
            filter_expr = 'select=not(mod(n\\,2))'
            print("📋 提取模式: 偶数帧（A视频 - 主内容）")

        cmd = [
            self.ffmpeg_cmd, '-y',
            '-i', ab_video_path,
            '-vf', filter_expr,
            '-c:v', self.encoding_params['codec'],
            '-preset', self.encoding_params['preset'],
            '-crf', self.encoding_params['crf'],
            '-c:a', 'copy',  # 复制音频流
            output_path
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print(f"✓ {extract_type}视频提取成功")

                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / 1024 / 1024
                    print(f"📁 文件大小: {file_size:.2f} MB")

                    info = self.get_video_info(output_path)
                    if info:
                        print(f"🎞️ 帧数: {info['nb_frames']}")
                        print(f"⏱️ 时长: {info['duration']:.2f}秒")

                return True
            else:
                print(f"✗ {extract_type}视频提取失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"提取出错: {e}")
            return False

    def detect_ab_interleaving(self, video_path):
        """检测视频是否使用了AB融帧技术"""
        print("🔍 检测AB融帧技术...")
        print(f"分析文件: {os.path.basename(video_path)}")

        info = self.get_video_info(video_path)
        if not info:
            print("✗ 无法获取视频信息")
            return False

        print(f"📊 基本信息:")
        print(f"   分辨率: {info['width']}x{info['height']}")
        print(f"   帧率: {info['fps']:.2f}fps")
        print(f"   时长: {info['duration']:.2f}秒")
        print(f"   总帧数: {info['nb_frames']}")

        # 计算理论帧数
        theoretical_frames = int(info['duration'] * info['fps'])
        frame_diff = info['nb_frames'] - theoretical_frames

        print(f"🧮 帧数分析:")
        print(f"   理论帧数: {theoretical_frames}")
        print(f"   实际帧数: {info['nb_frames']}")
        print(f"   差值: {frame_diff}")

        # 检测AB融帧特征
        ab_detected = False
        detection_reasons = []

        # 特征1: 帧数异常（接近2倍）
        if frame_diff > theoretical_frames * 0.8:
            ab_detected = True
            detection_reasons.append("帧数接近理论值的2倍")

        # 特征2: 轻微的帧数超出
        elif frame_diff > 10:
            ab_detected = True
            detection_reasons.append(f"帧数异常超出{frame_diff}帧")

        # 特征3: 检查元数据
        try:
            cmd = [self.ffprobe_cmd, '-v', 'error', '-show_format', video_path]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                if 'AB_FRAME_INTERLEAVING' in result.stdout or 'DUAL_REALITY' in result.stdout:
                    ab_detected = True
                    detection_reasons.append("发现AB融帧标识元数据")
        except:
            pass

        print(f"\n🎯 检测结果:")
        if ab_detected:
            print("🚨 检测到AB融帧技术!")
            print("📋 检测依据:")
            for reason in detection_reasons:
                print(f"   • {reason}")

            # 尝试提取验证
            print(f"\n🔬 验证测试:")
            temp_dir = tempfile.mkdtemp(prefix='ab_detection_')
            try:
                test_extract = os.path.join(temp_dir, 'test_extract.mp4')
                if self.extract_hidden_video(video_path, test_extract, 'B'):
                    extract_info = self.get_video_info(test_extract)
                    if extract_info and extract_info['nb_frames'] > 0:
                        print(f"✓ 成功提取隐藏视频: {extract_info['nb_frames']}帧")
                        return True
            finally:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
        else:
            print("✅ 未检测到AB融帧技术")

        return ab_detected

    def verify_compatibility(self, video_path):
        """验证视频的播放器兼容性"""
        print("🔍 播放器兼容性验证...")

        info = self.get_video_info(video_path)
        if not info:
            return False

        print(f"📋 编码兼容性检查:")
        print(f"   编码格式: {info['codec_name']}")

        # 检查H.264兼容性
        if info['codec_name'] == 'h264':
            print("   ✓ H.264编码 - 广泛兼容")
        else:
            print(f"   ⚠️ 非H.264编码 - 兼容性可能受限")

        # 检查分辨率
        if info['width'] == 1080 and info['height'] == 1920:
            print("   ✓ 竖屏HD分辨率 - 移动端优化")
        elif info['width'] == 1920 and info['height'] == 1080:
            print("   ✓ 横屏HD分辨率 - 桌面端优化")
        else:
            print(f"   ⚠️ 非标准分辨率: {info['width']}x{info['height']}")

        # 检查帧率
        if 29 <= info['fps'] <= 31:
            print("   ✓ 标准帧率 - 良好兼容性")
        else:
            print(f"   ⚠️ 非标准帧率: {info['fps']:.2f}fps")

        return True


def main():
    """主程序 - 使用示例"""
    toolkit = ABFrameInterleavingToolkit()

    print("🎬 AB融帧视频隐写技术工具包")
    print("基于966.mp4技术分析的精确复刻实现")
    print("=" * 60)

    # 示例文件路径（请根据实际情况修改）
    video_a = "test_video_a.mp4"  # 主视频
    video_b = "test_video_b.mp4"  # 隐藏视频
    output_ab = "ab_interleaved_output.mp4"  # 输出的AB融帧视频
    extracted_b = "extracted_hidden_video.mp4"  # 提取的隐藏视频

    print("📋 使用说明:")
    print("1. 请将测试视频文件放在当前目录")
    print("2. 修改main()函数中的文件路径")
    print("3. 运行程序进行AB融帧处理")
    print()

    # 检查测试文件是否存在
    if os.path.exists(video_a) and os.path.exists(video_b):
        print("🚀 开始AB融帧处理...")

        # 创建AB融帧视频
        success = toolkit.create_ab_interleaved_video(video_a, video_b, output_ab)

        if success:
            print("\n🔍 验证AB融帧技术...")
            # 检测AB融帧
            toolkit.detect_ab_interleaving(output_ab)

            print("\n🎯 提取隐藏视频...")
            # 提取隐藏视频
            toolkit.extract_hidden_video(output_ab, extracted_b, 'B')

            print("\n📱 兼容性验证...")
            # 验证兼容性
            toolkit.verify_compatibility(output_ab)

        print("\n✅ 处理完成!")
    else:
        print("⚠️ 测试文件不存在，请准备测试视频文件")
        print(f"需要文件: {video_a}, {video_b}")


if __name__ == "__main__":
    main()
