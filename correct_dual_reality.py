#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的双重现实视频实现
一个视频文件，包含A+B所有帧，普通播放器只看A视频，检测系统提取B视频
"""

import subprocess
import os
import tempfile
import shutil

def check_ffmpeg():
    """检查FFmpeg"""
    for cmd in ['ffmpeg', 'ffmpeg.exe']:
        try:
            result = subprocess.run([cmd, '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    raise RuntimeError("FFmpeg未找到")

def create_correct_dual_reality(video_a_path, video_b_path, output_path):
    """创建正确的双重现实视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    temp_dir = tempfile.mkdtemp(prefix='correct_dual_')
    
    try:
        print("创建正确的双重现实视频...")
        print(f"A视频: {video_a_path}")
        print(f"B视频: {video_b_path}")
        print(f"输出: {output_path}")
        
        # 方案：创建ABABAB...交替序列，但设置B帧为不可见
        # 使用特殊的编码参数让B帧存在但不显示
        
        # 1. 预处理视频，统一参数
        processed_a = os.path.join(temp_dir, 'a.mp4')
        processed_b = os.path.join(temp_dir, 'b.mp4')
        
        # 统一处理参数
        target_width = 1080
        target_height = 1920
        target_fps = 30

        # 处理A视频
        cmd_a = [
            ffmpeg_cmd, '-y', '-i', video_a_path,
            '-vf', f'scale={target_width}:{target_height},fps={target_fps},format=yuv420p',
            '-c:v', 'libx264', '-preset', 'fast', '-crf', '20',
            '-pix_fmt', 'yuv420p',
            processed_a
        ]

        print("处理A视频...")
        result = subprocess.run(cmd_a, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"A视频处理失败: {result.stderr}")
            return False

        # 处理B视频
        cmd_b = [
            ffmpeg_cmd, '-y', '-i', video_b_path,
            '-vf', f'scale={target_width}:{target_height},fps={target_fps},format=yuv420p',
            '-c:v', 'libx264', '-preset', 'fast', '-crf', '20',
            '-pix_fmt', 'yuv420p',
            processed_b
        ]

        print("处理B视频...")
        result = subprocess.run(cmd_b, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"B视频处理失败: {result.stderr}")
            return False

        # 2. 创建真正的双重现实视频 - 帧交替方案
        print("创建真正的双重现实序列...")

        # 提取帧文件进行精确控制
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        os.makedirs(frames_a_dir, exist_ok=True)
        os.makedirs(frames_b_dir, exist_ok=True)

        # 提取A视频所有帧
        cmd_extract_a = [
            ffmpeg_cmd, '-y', '-i', processed_a,
            os.path.join(frames_a_dir, 'frame_%04d.png')
        ]

        print("提取A视频帧...")
        result = subprocess.run(cmd_extract_a, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"A帧提取失败: {result.stderr}")
            return False

        # 提取B视频所有帧
        cmd_extract_b = [
            ffmpeg_cmd, '-y', '-i', processed_b,
            os.path.join(frames_b_dir, 'frame_%04d.png')
        ]

        print("提取B视频帧...")
        result = subprocess.run(cmd_extract_b, capture_output=True, text=True, timeout=300)
        if result.returncode != 0:
            print(f"B帧提取失败: {result.stderr}")
            return False

        # 创建交替帧序列
        success = create_interleaved_video(frames_a_dir, frames_b_dir, output_path, temp_dir)
        return success

    except Exception as e:
        print(f"处理出错: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def create_interleaved_video(frames_a_dir, frames_b_dir, output_path, temp_dir):
    """创建真正的帧交替视频"""

    ffmpeg_cmd = check_ffmpeg()

    # 获取帧文件列表
    frames_a = sorted([f for f in os.listdir(frames_a_dir) if f.endswith('.png')])
    frames_b = sorted([f for f in os.listdir(frames_b_dir) if f.endswith('.png')])

    print(f"A视频帧数: {len(frames_a)}")
    print(f"B视频帧数: {len(frames_b)}")

    # 创建交替帧列表：A1 B1 A2 B2 A3 B3...
    frame_list_file = os.path.join(temp_dir, 'interleaved_frames.txt')

    with open(frame_list_file, 'w') as f:
        max_frames = min(len(frames_a), len(frames_b))
        total_frames = 0

        for i in range(max_frames):
            # A帧 (偶数位置)
            a_frame_path = os.path.join(frames_a_dir, frames_a[i])
            f.write(f"file '{a_frame_path}'\n")
            f.write(f"duration 0.033333\n")  # 30fps
            total_frames += 1

            # B帧 (奇数位置)
            b_frame_path = os.path.join(frames_b_dir, frames_b[i])
            f.write(f"file '{b_frame_path}'\n")
            f.write(f"duration 0.033333\n")  # 30fps
            total_frames += 1

    print(f"创建交替序列，总帧数: {total_frames}")

    # 使用concat demuxer创建交替视频
    cmd = [
        ffmpeg_cmd, '-y',
        '-f', 'concat',
        '-safe', '0',
        '-i', frame_list_file,

        # 关键：使用特殊编码参数实现PTS操控效果
        '-c:v', 'libx264',
        '-preset', 'medium',
        '-crf', '18',
        '-r', '30',

        # 重要：GOP结构控制
        '-g', '60',  # 大GOP确保结构稳定
        '-keyint_min', '60',
        '-sc_threshold', '0',

        # 添加双重现实标识
        '-metadata', 'comment=TRUE_DUAL_REALITY_V2.0',
        '-metadata', 'frame_pattern=A_EVEN_B_ODD_INTERLEAVED',
        '-metadata', 'total_ab_frames=' + str(total_frames),

        output_path
    ]

    print("编码交替视频...")
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)

    if result.returncode == 0:
        print("✓ 交替视频创建成功")
        return True
    else:
        print(f"✗ 交替视频创建失败: {result.stderr}")
        return False
        
        print("应用双重现实编码...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"✓ 双重现实视频创建成功: {output_path}")
            
            # 验证结果
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024 / 1024
                print(f"文件大小: {file_size:.2f} MB")
                
                # 获取帧数
                probe_cmd = [
                    'ffprobe', '-v', 'error', '-select_streams', 'v:0',
                    '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0',
                    output_path
                ]
                
                try:
                    probe_result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
                    if probe_result.returncode == 0:
                        frame_count = probe_result.stdout.strip()
                        print(f"总帧数: {frame_count}")
                except:
                    print("无法获取帧数信息")
            
            return True
        else:
            print(f"✗ 双重现实视频创建失败: {result.stderr}")
            return False

def extract_b_video(dual_video_path, output_b_path):
    """从双重现实视频提取B视频"""
    
    ffmpeg_cmd = check_ffmpeg()
    
    print("提取B视频...")
    print(f"输入: {dual_video_path}")
    print(f"输出: {output_b_path}")
    
    # 提取奇数帧（B视频）
    cmd = [
        ffmpeg_cmd, '-y',
        '-i', dual_video_path,
        
        # 选择奇数帧
        '-vf', 'select=mod(n\\,2)',
        
        '-c:v', 'libx264',
        '-preset', 'medium',
        '-crf', '18',
        
        output_b_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✓ B视频提取成功: {output_b_path}")
            
            if os.path.exists(output_b_path):
                file_size = os.path.getsize(output_b_path) / 1024 / 1024
                print(f"B视频大小: {file_size:.2f} MB")
            
            return True
        else:
            print(f"✗ B视频提取失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"提取出错: {e}")
        return False

def main():
    """主程序"""
    
    # 指定文件路径
    video_a = r"C:\Users\<USER>\Desktop\下载的\AA.mp4"
    video_b = r"C:\Users\<USER>\Desktop\下载的\IMG_1915.MP4"
    output_dual = r"C:\Users\<USER>\Desktop\下载的\correct_dual_reality.mp4"
    output_extracted_b = r"C:\Users\<USER>\Desktop\下载的\correct_extracted_b.mp4"
    
    print("=" * 80)
    print("正确的双重现实视频系统")
    print("=" * 80)
    print("目标：一个视频文件，包含A+B所有帧")
    print("效果：普通播放器只看A视频，检测系统提取B视频")
    print("=" * 80)
    
    # 检查输入文件
    if not os.path.exists(video_a):
        print(f"✗ A视频不存在: {video_a}")
        return
    
    if not os.path.exists(video_b):
        print(f"✗ B视频不存在: {video_b}")
        return
    
    print(f"✓ A视频: {os.path.basename(video_a)}")
    print(f"✓ B视频: {os.path.basename(video_b)}")
    
    # 1. 创建双重现实视频
    print(f"\n步骤1: 创建双重现实视频")
    print("-" * 40)
    
    success = create_correct_dual_reality(video_a, video_b, output_dual)
    if not success:
        print("双重现实视频创建失败")
        return
    
    # 2. 提取B视频验证
    print(f"\n步骤2: 提取B视频验证")
    print("-" * 40)
    
    success = extract_b_video(output_dual, output_extracted_b)
    
    print(f"\n" + "=" * 80)
    print("处理完成!")
    print("=" * 80)
    print(f"双重现实视频: {os.path.basename(output_dual)}")
    print(f"提取的B视频: {os.path.basename(output_extracted_b)}")
    print(f"\n请验证:")
    print(f"1. 播放双重现实视频 - 应该只看到A视频内容")
    print(f"2. 查看提取的B视频 - 应该是B视频内容")

if __name__ == "__main__":
    main()
